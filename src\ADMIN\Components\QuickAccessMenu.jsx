import React from 'react';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  Chip,
  Avatar,
  styled,
  Paper,
    Slide,
    alpha,

} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  AdminPanelSettings as AdminPanelIcon,
  CalendarToday as CalendarIcon,
  Mail as MessagesIcon,
  Settings as SettingsIcon,
  People as UsersIcon,
  Assessment as ReportsIcon,
  Folder as FileManagerIcon,
    Apps as AppsIcon,
} from '@mui/icons-material';

const QuickAccessMenuContainer = styled(Paper)(({ theme, isdarkmode }) => ({
  width: 320,
  borderRadius: '16px',
  background: isdarkmode === 'true'
    ? `linear-gradient(145deg, ${theme.palette.background.paper}, ${theme.palette.background.default})`
    : theme.palette.background.paper,
  boxShadow: theme.shadows[10],
  border: isdarkmode === 'true' 
    ? '1px solid rgba(255, 255, 255, 0.12)' 
    : '1px solid rgba(0, 0, 0, 0.08)',
  overflow: 'hidden',
}));

const MenuHeader = styled(Box)(({ theme, isdarkmode }) => ({
  padding: theme.spacing(2),
  background: isdarkmode === 'true'
    ? `linear-gradient(145deg, ${alpha(theme.palette.primary.dark, 0.9)}, ${alpha(theme.palette.primary.main, 0.9)})`
    : `linear-gradient(145deg, ${alpha(theme.palette.primary.light, 0.9)}, ${alpha(theme.palette.primary.main, 0.9)})`,
  color: theme.palette.common.white,
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
}));

const StyledMenuItem = styled(MenuItem)(({ theme, isdarkmode }) => ({
  padding: theme.spacing(1.5, 2),
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: isdarkmode === 'true'
      ? alpha(theme.palette.primary.main, 0.1)
      : alpha(theme.palette.primary.light, 0.1),
    transform: 'translateX(5px)',
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
    },
  },
  '& .MuiListItemIcon-root': {
    minWidth: '40px',
    color: isdarkmode === 'true' 
      ? theme.palette.text.secondary 
      : theme.palette.text.primary,
  },
}));

const QuickAccessMenu = ({ 
  anchorEl, 
  open, 
  onClose, 
  items = [], 
  isDarkMode 
}) => {
  // Default quick access items if none provided
  const defaultItems = [
    { 
      icon: <DashboardIcon />, 
      label: 'Dashboard', 
      path: '/admin/dashboard',
      description: 'Main admin dashboard',
    },
    { 
      icon: <AdminPanelIcon />, 
      label: 'Admin Panel', 
      path: '/admin/panel',
      description: 'System configuration',
    },
    { 
      icon: <UsersIcon />, 
      label: 'User Management', 
      path: '/admin/users',
      description: 'Manage all users',
    },
    { 
      icon: <ReportsIcon />, 
      label: 'Reports', 
      path: '/admin/reports',
      description: 'View system reports',
    },
    { 
      icon: <FileManagerIcon />, 
      label: 'File Manager', 
      path: '/admin/files',
      description: 'Manage system files',
    },
    { 
      icon: <CalendarIcon />, 
      label: 'Calendar', 
      path: '/admin/calendar',
      description: 'Schedule events',
    },
    { 
      icon: <MessagesIcon />, 
      label: 'Messages', 
      path: '/admin/messages',
      description: 'Internal messaging',
    },
    { 
      icon: <SettingsIcon />, 
      label: 'Settings', 
      path: '/admin/settings',
      description: 'System settings',
    },
  ];

  const menuItems = items.length > 0 ? items : defaultItems;

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      TransitionComponent={Slide}
      transitionDuration={250}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      sx={{
        '& .MuiPaper-root': {
          backgroundColor: 'transparent',
          boxShadow: 'none',
          overflow: 'visible',
        },
      }}
    >
      <QuickAccessMenuContainer isdarkmode={isDarkMode.toString()}>
        <MenuHeader isdarkmode={isDarkMode.toString()}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              backgroundColor: 'rgba(255,255,255,0.2)',
              backdropFilter: 'blur(5px)',
            }}
          >
            <AppsIcon />
          </Avatar>
          <Box>
            <Typography variant="subtitle1" fontWeight="bold">
              Quick Access
            </Typography>
            <Typography variant="caption">
              Jump to important sections
            </Typography>
          </Box>
        </MenuHeader>

        <Box sx={{ maxHeight: '400px', overflowY: 'auto' }}>
          {menuItems.map((item, index) => (
            <React.Fragment key={index}>
              <StyledMenuItem 
                isdarkmode={isDarkMode.toString()}
                onClick={() => {
                  onClose();
                  // In a real app, you would navigate to the item.path
                  console.log(`Navigating to: ${item.path}`);
                }}
              >
                <ListItemIcon>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  secondary={item.description}
                  secondaryTypographyProps={{
                    variant: 'caption',
                    color: 'text.secondary',
                  }}
                />
                {index === 0 && (
                  <Chip 
                    label="New" 
                    size="small" 
                    color="primary" 
                    sx={{ ml: 1 }} 
                  />
                )}
              </StyledMenuItem>
              {(index === 2 || index === 5) && (
                <Divider sx={{ my: 0.5 }} />
              )}
            </React.Fragment>
          ))}
        </Box>

        <Divider sx={{ my: 0 }} />

        <Box sx={{ 
          p: 1.5, 
          textAlign: 'center',
          backgroundColor: isDarkMode 
            ? 'rgba(255,255,255,0.05)' 
            : 'rgba(0,0,0,0.03)',
        }}>
          <Typography variant="caption" color="text.secondary">
            {menuItems.length} quick access items
          </Typography>
        </Box>
      </QuickAccessMenuContainer>
    </Menu>
  );
};

export default QuickAccessMenu;