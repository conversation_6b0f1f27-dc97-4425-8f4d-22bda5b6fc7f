import React from 'react';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import {ThemeProvider} from './Assets/ThemeContext';
import { createRoot } from 'react-dom/client';


const container = document.getElementById('root');
const root = createRoot(container);


root.render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>,
  document.getElementById('root')
);


reportWebVitals();
