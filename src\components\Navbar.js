import React, { useState } from 'react';
import {
  AppBar,
  <PERSON>lbar,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Box,
  InputBase,
  Badge,
  Slide,
  Tooltip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Person as PersonIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { useTheme } from '../Assets/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ConfirmationDialog from './UI/ConfirmationDialog';
import ProfileDialog from './ProfileDialog';
import LanguageSelector from './LanguageSelector';
import NotificationsNavbar from './NotificationsNavbar'; 
 // New component import

// Styled Components
const ModernAppBar = styled(AppBar)(({ theme, isDarkMode }) => ({
  background: isDarkMode
    ? 'linear-gradient(145deg, rgba(44, 62, 80, 0.95), rgba(52, 73, 94, 0.95))'
    : 'linear-gradient(145deg, rgba(63, 81, 181, 0.95), rgba(0, 63, 114, 0.95))',
  backdropFilter: 'blur(10px)',
  borderRadius: '16px',
  margin: '8px',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
  border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
}));



const SearchBar = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: theme.palette.mode === 'dark' 
    ? 'rgba(255, 255, 255, 0.15)' 
    : 'rgba(255, 255, 255, 0.8)', // Increased opacity for light mode visibility
  borderRadius: '20px',
  padding: '4px 12px',
  transition: 'width 0.3s ease, background-color 0.3s ease',
  width: '200px',
  '&:hover': {
    width: '250px',
    backgroundColor: theme.palette.mode === 'dark' 
      ? 'rgba(255, 255, 255, 0.25)' 
      : 'rgba(255, 255, 255, 1)',
  },
}));

const ProfileMenu = styled(Menu)(({ theme, isDarkMode }) => ({
  '& .MuiPaper-root': {
    width: '280px',
    borderRadius: '16px',
    background: isDarkMode
      ? 'linear-gradient(145deg, #2c3e50, #34495e)'
      : 'linear-gradient(145deg, #ffffff, #f5f5f5)',
    boxShadow: '0 10px 40px rgba(0, 0, 0, 0.25)',
    border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
    overflow: 'hidden',
  },
}));

const MenuItemStyled = styled(MenuItem)(({ theme, isDarkMode }) => ({
  padding: '12px 16px',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
    transform: 'translateX(6px)',
    '& .MuiListItemIcon-root': {
      color: isDarkMode ? '#42a5f5' : '#1976d2',
    },
  },
}));

const Navbar = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [profileDialogOpen, setProfileDialogOpen] = useState(false);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogoutClick = () => {
    setLogoutDialogOpen(true);
    handleMenuClose();
  };

  const handleLogoutDialogClose = () => {
    setLogoutDialogOpen(false);
  };

  const handleLogoutConfirm = () => {
    navigate('/login');
    handleLogoutDialogClose();
  };

  const handleProfileClick = () => {
    setProfileDialogOpen(true);
    handleMenuClose();
  };

  const handleProfileDialogClose = () => {
    setProfileDialogOpen(false);
  };

  const handleNotificationsOpen = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };

  return (
    <>
      <ModernAppBar position="static" isDarkMode={isDarkMode}>
        <Toolbar sx={{ justifyContent: 'space-between', padding: '0 16px' }}>
          {/* Middle Section: Search */}
          <SearchBar>
            <SearchIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5', mr: 1 }} />
            <InputBase
              placeholder={t('search') + '...'}
              sx={{ color: isDarkMode ? '#fff' : '#333', width: '100%' }} // Adjusted text color for light mode
            />
          </SearchBar>

          {/* Right Section: Actions */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <LanguageSelector />
            <Tooltip title={t('notifications')}>
              <IconButton
                color="inherit"
                onClick={handleNotificationsOpen}
                sx={{ transition: 'all 0.3s ease', '&:hover': { transform: 'scale(1.1)' } }}
              >
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon sx={{ color: '#fff' }} />
                </Badge>
              </IconButton>
            </Tooltip>
            <IconButton
              onClick={handleProfileMenuOpen}
              sx={{ transition: 'all 0.3s ease', '&:hover': { transform: 'scale(1.1)' } }}
            >
              <Avatar
                alt="User"
                src="/path/to/user-image.jpg"
                sx={{
                  width: 40,
                  height: 40,
                  border: `2px solid ${isDarkMode ? '#90caf9' : '#3f51b5'}`,
                  transition: 'transform 0.3s ease',
                }}
              />
            </IconButton>

            {/* Profile Menu */}
            <ProfileMenu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              isDarkMode={isDarkMode}
              TransitionComponent={Slide}
              transitionDuration={300}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <Box
                sx={{
                  p: 2,
                  background: isDarkMode ? 'rgba(30, 42, 56, 0.9)' : 'rgba(245, 245, 245, 0.9)',
                  borderBottom: `1px solid ${isDarkMode ? '#3d4e60' : '#e0e0e0'}`,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <Avatar
                  alt="User"
                  src="/path/to/user-image.jpg"
                  sx={{ width: 56, height: 56, border: `2px solid ${isDarkMode ? '#90caf9' : '#3f51b5'}` }}
                />
                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{ fontWeight: 600, color: isDarkMode ? '#fff' : '#000' }}
                  >
                    John Doe
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }}
                  >
                    <EMAIL>
                  </Typography>
                </Box>
              </Box>
              <MenuItemStyled isDarkMode={isDarkMode} onClick={handleProfileClick}>
                <ListItemIcon><PersonIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }} /></ListItemIcon>
                <ListItemText primary={t('profile')} />
              </MenuItemStyled>
              <MenuItemStyled isDarkMode={isDarkMode} onClick={handleMenuClose}>
                <ListItemIcon><SettingsIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }} /></ListItemIcon>
                <ListItemText primary={t('settings')} />
              </MenuItemStyled>
              <Divider sx={{ my: 1, backgroundColor: isDarkMode ? '#3d4e60' : '#e0e0e0' }} />
              <MenuItemStyled isDarkMode={isDarkMode} onClick={handleLogoutClick}>
                <ListItemIcon><LogoutIcon sx={{ color: isDarkMode ? '#ff6b6b' : '#f44336' }} /></ListItemIcon>
                <ListItemText primary={t('logout')} />
              </MenuItemStyled>
              <Box
                sx={{
                  p: 1,
                  textAlign: 'center',
                  background: isDarkMode ? 'rgba(30, 42, 56, 0.9)' : 'rgba(245, 245, 245, 0.9)',
                  borderTop: `1px solid ${isDarkMode ? '#3d4e60' : '#e0e0e0'}`,
                }}
              >
                <Typography variant="caption" sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }}>
                  v1.0.0
                </Typography>
              </Box>
            </ProfileMenu>

            {/* Notifications Dropdown */}
            <NotificationsNavbar
              anchorEl={notificationsAnchorEl}
              open={Boolean(notificationsAnchorEl)}
              onClose={handleNotificationsClose}
              isDarkMode={isDarkMode}
            />
          </Box>
        </Toolbar>
      </ModernAppBar>

      <ConfirmationDialog
        open={logoutDialogOpen}
        onClose={handleLogoutDialogClose}
        onConfirm={handleLogoutConfirm}
        title="Confirm Logout"
        message="Are you sure you want to logout?"
      />

      <ProfileDialog open={profileDialogOpen} onClose={handleProfileDialogClose} />
    </>
  );
};

export default Navbar;