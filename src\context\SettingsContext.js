// src/context/SettingsContext.js
import { createContext, useContext, useState } from 'react';

const SettingsContext = createContext();

export const SettingsProvider = ({ children }) => {
  const [sidebarColor, setSidebarColor] = useState('#34495e');
  const [navbarColor, setNavbarColor] = useState('#3f51b5');
  const [sidebarBg, setSidebarBg] = useState(null);
  const [bgOpacity, setBgOpacity] = useState(0.3);

  return (
    <SettingsContext.Provider
      value={{
        sidebarColor,
        setSidebarColor,
        navbarColor,
        setNavbarColor,
        sidebarBg,
        setSidebarBg,
        bgOpacity,
        setBgOpacity,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};

// Make sure this is exported
export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};