import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Avatar,
  Typography,
  Box,
  IconButton,
  styled,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import EmailIcon from '@mui/icons-material/Email';
import InfoIcon from '@mui/icons-material/Info';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, ResponsiveContainer } from 'recharts';

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiPaper-root': {
    width: '100%',
    maxWidth: '600px',
    borderRadius: '16px',
    background: theme.palette.mode === 'dark' 
      ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' 
      : 'linear-gradient(145deg, #ffffff 0%, #f5f5f5 100%)',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 8px 30px rgba(0, 0, 0, 0.7)' 
      : '0 8px 30px rgba(0, 0, 0, 0.25)',
    border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
    overflow: 'hidden',
  },
}));

const StyledAvatar = styled(Avatar)(({ theme }) => ({
  width: 120,
  height: 120,
  border: `3px solid ${theme.palette.mode === 'dark' ? '#90caf9' : '#3f51b5'}`,
  transition: 'transform 0.3s ease',
  '&:hover': { transform: 'scale(1.05)' },
}));

const FieldBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(1.5),
  borderRadius: '12px',
  backgroundColor: theme.palette.mode === 'dark' ? '#2e3b3e' : '#fafafa',
  marginBottom: theme.spacing(2),
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#455a64' : '#e3f2fd',
    transform: 'translateX(5px)',
  },
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: '20px',
  padding: theme.spacing(1, 3),
  textTransform: 'none',
  fontWeight: 600,
  transition: 'all 0.3s ease',
  '&:hover': { transform: 'scale(1.05)' },
}));

const ProfileDialog = ({ open, onClose }) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  const [user, setUser] = useState({
    name: 'John Doe',
    email: '<EMAIL>',
    bio: 'Software Engineer | React Enthusiast',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  });

  const [isEditing, setIsEditing] = useState(false);

  const chartData = [
    { name: 'Projects', value: 400 },
    { name: 'Tasks', value: 300 },
    { name: 'Meetings', value: 200 },
  ];
  const COLORS = isDarkMode ? ['#42a5f5', '#66bb6a', '#ffa726'] : ['#2196f3', '#4caf50', '#ff9800'];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setUser((prevUser) => ({ ...prevUser, [name]: value }));
  };

  const handleEditToggle = () => setIsEditing(!isEditing);
  const handleSave = () => {
    setIsEditing(false);
    onClose();
  };

  return (
    <StyledDialog open={open} onClose={onClose} aria-labelledby="profile-dialog-title">
      <DialogTitle
        sx={{
          bgcolor: isDarkMode ? '#37474f' : '#3f51b5',
          color: '#fff',
          fontWeight: 700,
          textAlign: 'center',
          py: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: isDarkMode ? '1px solid #455a64' : 'none',
        }}
      >
        <Typography variant="h6">Profile Dashboard</Typography>
        <IconButton onClick={onClose} sx={{ color: '#fff' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <StyledAvatar alt={user.name} src={user.avatar} />
        </Box>

        <FieldBox>
          <PersonIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }} />
          {isEditing ? (
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={user.name}
              onChange={handleChange}
              variant="outlined"
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px', color: isDarkMode ? '#e0e0e0' : 'inherit' } }}
            />
          ) : (
            <Typography variant="body1" sx={{ fontWeight: 500, color: isDarkMode ? '#e0e0e0' : '#333' }}>
              {user.name}
            </Typography>
          )}
        </FieldBox>

        <FieldBox>
          <EmailIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }} />
          {isEditing ? (
            <TextField
              fullWidth
              label="Email"
              name="email"
              value={user.email}
              onChange={handleChange}
              variant="outlined"
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px', color: isDarkMode ? '#e0e0e0' : 'inherit' } }}
            />
          ) : (
            <Typography variant="body1" sx={{ fontWeight: 500, color: isDarkMode ? '#e0e0e0' : '#333' }}>
              {user.email}
            </Typography>
          )}
        </FieldBox>

        <FieldBox>
          <InfoIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }} />
          {isEditing ? (
            <TextField
              fullWidth
              label="Bio"
              name="bio"
              value={user.bio}
              onChange={handleChange}
              variant="outlined"
              multiline
              rows={3}
              sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px', color: isDarkMode ? '#e0e0e0' : 'inherit' } }}
            />
          ) : (
            <Typography variant="body1" sx={{ fontWeight: 500, color: isDarkMode ? '#e0e0e0' : '#333' }}>
              {user.bio}
            </Typography>
          )}
        </FieldBox>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, color: isDarkMode ? '#e0e0e0' : '#1e3c72', mb: 2 }}>
            Activity Overview
          </Typography>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: isDarkMode ? '#37474f' : '#fff',
                  borderRadius: '8px',
                  border: 'none',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                  color: isDarkMode ? '#e0e0e0' : 'text.primary',
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, justifyContent: 'space-between' }}>
        <ActionButton
          onClick={onClose}
          sx={{ color: isDarkMode ? '#e0e0e0' : '#3f51b5', bgcolor: 'transparent' }}
        >
          Cancel
        </ActionButton>
        {isEditing ? (
          <ActionButton
            onClick={handleSave}
            startIcon={<SaveIcon />}
            sx={{ bgcolor: isDarkMode ? '#0288d1' : '#2196f3', color: '#fff', '&:hover': { bgcolor: isDarkMode ? '#0277bd' : '#1976d2' } }}
          >
            Save
          </ActionButton>
        ) : (
          <ActionButton
            onClick={handleEditToggle}
            startIcon={<EditIcon />}
            sx={{ bgcolor: isDarkMode ? '#388e3c' : '#4caf50', color: '#fff', '&:hover': { bgcolor: isDarkMode ? '#2e7d32' : '#388e3c' } }}
          >
            Edit
          </ActionButton>
        )}
      </DialogActions>
    </StyledDialog>
  );
};

export default ProfileDialog;