import React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Button from '@mui/material/Button';
import 'bootstrap/dist/css/bootstrap.min.css'; // Bootstrap CSS

const CustomCard = ({ title, content, actionLabel, onActionClick, className = '' }) => {
  return (
    <Card className={`m-2 ${className}`}>
      <CardContent>
        <h5>{title}</h5>
        <p>{content}</p>
      </CardContent>
      {actionLabel && (
        <CardActions>
          <Button onClick={onActionClick} color="primary">
            {actionLabel}
          </Button>
        </CardActions>
      )}
    </Card>
  );
};

export default CustomCard;