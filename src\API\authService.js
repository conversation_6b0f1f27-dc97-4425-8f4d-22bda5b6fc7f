import axios from 'axios';

// Create an Axios instance with base configuration
const apiClient = axios.create({
  baseURL: 'http://localhost:8088',
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Authenticates a user by sending credentials to the backend.
 * @param {Object} credentials - The user's email and password { email, password }.
 * @returns {Promise<Object>} The authentication response with token and user details { token, userId, email, role }.
 * @throws {Object} Error response { error } if authentication fails.
 */
export const authenticate = async (credentials) => {
  try {
    const response = await apiClient.post('/api/auth/authenticate', credentials);
    const backendResponse = response.data;
    // Transform the backend response to match the expected frontend structure
    return {
      token: backendResponse.token,
      userId: backendResponse.user.userId,
      email: backendResponse.user.email,
      role: {
        // Since backend returns role as a string, wrap it in an object
        id: '', // Role ID isn't provided; leave empty or fetch separately if needed
        role: backendResponse.user.role
      }
    };
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};