import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Typography,
  Avatar,
  Divider,
  styled,
} from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { useTheme } from '@mui/material/styles';

const NotificationMenu = styled(Menu)(({ theme }) => ({
  '& .MuiPaper-root': {
    width: '350px',
    maxHeight: '400px',
    overflowY: 'auto',
    borderRadius: '12px',
    background: theme.palette.mode === 'dark' 
      ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' 
      : '#fff',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 6px 25px rgba(0, 0, 0, 0.7)' 
      : '0 4px 20px rgba(0, 0, 0, 0.15)',
    border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
  },
}));

const NotificationItem = styled(MenuItem)(({ theme }) => ({
  padding: theme.spacing(1.5),
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#455a64' : '#f5f5f5',
    transform: 'translateX(5px)',
  },
}));

const Notifications = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  const notifications = [
    { id: 1, user: 'John Doe', avatar: 'https://randomuser.me/api/portraits/men/1.jpg', message: 'Assigned you to Project X', time: '2 mins ago' },
    { id: 2, user: 'Jane Smith', avatar: 'https://randomuser.me/api/portraits/women/2.jpg', message: 'Commented on your invoice', time: '15 mins ago' },
    { id: 3, user: 'Mike Johnson', avatar: 'https://randomuser.me/api/portraits/men/3.jpg', message: 'Updated inventory status', time: '1 hour ago' },
    { id: 4, user: 'Sarah Williams', avatar: 'https://randomuser.me/api/portraits/women/4.jpg', message: 'Requested a report', time: '3 hours ago' },
  ];

  const handleOpen = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  return (
    <>
      <IconButton color="inherit" onClick={handleOpen}>
        <Badge badgeContent={notifications.length} color="error">
          <NotificationsIcon sx={{ fontSize: '1.5rem' }} />
        </Badge>
      </IconButton>

      <NotificationMenu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Box sx={{ p: 2 }}>
          <Typography
            variant="h6"
            sx={{ fontWeight: 600, color: isDarkMode ? '#e0e0e0' : '#1e3c72' }}
          >
            Notifications
          </Typography>
        </Box>
        <Divider sx={{ bgcolor: isDarkMode ? '#455a64' : '#e0e0e0' }} />
        {notifications.length > 0 ? (
          notifications.map((notification) => (
            <React.Fragment key={notification.id}>
              <NotificationItem onClick={handleClose}>
                <Avatar src={notification.avatar} sx={{ width: 40, height: 40, mr: 2, border: isDarkMode ? '2px solid #455a64' : 'none' }} />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body1" sx={{ fontWeight: 500, color: isDarkMode ? '#e0e0e0' : '#333' }}>
                    {notification.user}
                  </Typography>
                  <Typography variant="body2" sx={{ color: isDarkMode ? '#b0bec5' : '#666' }}>
                    {notification.message}
                  </Typography>
                  <Typography variant="caption" sx={{ color: isDarkMode ? '#90caf9' : '#888' }}>
                    {notification.time}
                  </Typography>
                </Box>
              </NotificationItem>
              <Divider sx={{ bgcolor: isDarkMode ? '#455a64' : '#e0e0e0' }} />
            </React.Fragment>
          ))
        ) : (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ color: isDarkMode ? '#b0bec5' : '#666' }}>
              No new notifications
            </Typography>
          </Box>
        )}
      </NotificationMenu>
    </>
  );
};

export default Notifications;