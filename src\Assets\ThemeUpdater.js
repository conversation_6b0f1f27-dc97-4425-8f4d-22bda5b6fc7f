// src/components/ThemeUpdater.js
import { useEffect } from 'react';
import { useSettings } from '../context/SettingsContext';

const ThemeUpdater = () => {
  const { sidebarColor, navbarColor } = useSettings();

  useEffect(() => {
    document.documentElement.style.setProperty('--sidebar-color', sidebarColor);
    document.documentElement.style.setProperty('--navbar-color', navbarColor);
  }, [sidebarColor, navbarColor]);

  return null; // This component doesn't render anything
};

export default ThemeUpdater;