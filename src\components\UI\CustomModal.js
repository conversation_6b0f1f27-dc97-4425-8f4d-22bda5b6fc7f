import React from 'react';
import Modal from '@mui/material/Modal';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import 'bootstrap/dist/css/bootstrap.min.css'; // Bootstrap CSS

const CustomModal = ({ isOpen, onClose, title, children, onConfirm, confirmLabel = 'Confirm', cancelLabel = 'Cancel' }) => {
  return (
    <Modal open={isOpen} onClose={onClose}>
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 400,
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 4,
        }}
      >
        <h5>{title}</h5>
        <div>{children}</div>
        <div className="mt-3">
          <Button onClick={onClose} className="me-2">
            {cancelLabel}
          </Button>
          <Button onClick={onConfirm} variant="contained" color="primary">
            {confirmLabel}
          </Button>
        </div>
      </Box>
    </Modal>
  );
};

export default CustomModal;