package com.ppg.ems_server_side_v0.service.core.implementation;

import com.ppg.ems_server_side_v0.domain.JobDescription;
import com.ppg.ems_server_side_v0.domain.User;
import com.ppg.ems_server_side_v0.model.api.request.JobDescriptionDTO;
import com.ppg.ems_server_side_v0.model.api.response.JobDescriptionResponse;
import com.ppg.ems_server_side_v0.repository.JobDescriptionRepository;
import com.ppg.ems_server_side_v0.repository.UserRepository;
import com.ppg.ems_server_side_v0.service.core.JobDescriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class JobDescriptionServiceImpl implements JobDescriptionService {

    private final JobDescriptionRepository jobDescriptionRepository;
    private final UserRepository userRepository;

    @Override
    public JobDescriptionResponse createJobDescription(JobDescriptionDTO jobDescriptionDTO, String userId) {
        log.info("Creating job description: {} for user: {}", jobDescriptionDTO.title(), userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        JobDescription jobDescription = JobDescription.builder()
                .title(jobDescriptionDTO.title())
                .description(jobDescriptionDTO.description())
                .requirements(jobDescriptionDTO.requirements())
                .responsibilities(jobDescriptionDTO.responsibilities())
                .department(jobDescriptionDTO.department())
                .location(jobDescriptionDTO.location())
                .salaryRange(jobDescriptionDTO.salaryRange())
                .experienceLevel(jobDescriptionDTO.experienceLevel())
                .createdBy(user)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        JobDescription savedJobDescription = jobDescriptionRepository.save(jobDescription);
        log.info("Created job description with ID: {}", savedJobDescription.getId());
        
        return mapToResponse(savedJobDescription);
    }

    @Override
    public JobDescriptionResponse updateJobDescription(String id, JobDescriptionDTO jobDescriptionDTO) {
        log.info("Updating job description with ID: {}", id);
        
        JobDescription jobDescription = jobDescriptionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Job description not found with ID: " + id));

        jobDescription.setTitle(jobDescriptionDTO.title());
        jobDescription.setDescription(jobDescriptionDTO.description());
        jobDescription.setRequirements(jobDescriptionDTO.requirements());
        jobDescription.setResponsibilities(jobDescriptionDTO.responsibilities());
        jobDescription.setDepartment(jobDescriptionDTO.department());
        jobDescription.setLocation(jobDescriptionDTO.location());
        jobDescription.setSalaryRange(jobDescriptionDTO.salaryRange());
        jobDescription.setExperienceLevel(jobDescriptionDTO.experienceLevel());
        jobDescription.setUpdatedAt(LocalDateTime.now());

        JobDescription updatedJobDescription = jobDescriptionRepository.save(jobDescription);
        log.info("Updated job description with ID: {}", id);
        
        return mapToResponse(updatedJobDescription);
    }

    @Override
    public void deleteJobDescription(String id) {
        log.info("Deleting job description with ID: {}", id);
        
        if (!jobDescriptionRepository.existsById(id)) {
            throw new RuntimeException("Job description not found with ID: " + id);
        }
        
        jobDescriptionRepository.deleteById(id);
        log.info("Deleted job description with ID: {}", id);
    }

    @Override
    public JobDescriptionResponse getJobDescriptionById(String id) {
        log.info("Fetching job description with ID: {}", id);
        
        JobDescription jobDescription = jobDescriptionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Job description not found with ID: " + id));
        
        return mapToResponse(jobDescription);
    }

    @Override
    public List<JobDescriptionResponse> getAllJobDescriptions() {
        log.info("Fetching all job descriptions");
        
        List<JobDescription> jobDescriptions = jobDescriptionRepository.findAll();
        log.info("Found {} job descriptions", jobDescriptions.size());
        
        return jobDescriptions.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<JobDescriptionResponse> getJobDescriptionsByDepartment(String department) {
        log.info("Fetching job descriptions for department: {}", department);
        
        List<JobDescription> jobDescriptions = jobDescriptionRepository.findByDepartmentOrderByCreatedAtDesc(department);
        log.info("Found {} job descriptions for department: {}", jobDescriptions.size(), department);
        
        return jobDescriptions.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<JobDescriptionResponse> getJobDescriptionsByUser(String userId) {
        log.info("Fetching job descriptions created by user: {}", userId);
        
        List<JobDescription> jobDescriptions = jobDescriptionRepository.findByCreatedByIdOrderByCreatedAtDesc(userId);
        log.info("Found {} job descriptions created by user: {}", jobDescriptions.size(), userId);
        
        return jobDescriptions.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<JobDescriptionResponse> searchJobDescriptions(String keyword) {
        log.info("Searching job descriptions with keyword: {}", keyword);
        
        List<JobDescription> jobDescriptions = jobDescriptionRepository.findByKeyword(keyword);
        log.info("Found {} job descriptions matching keyword: {}", jobDescriptions.size(), keyword);
        
        return jobDescriptions.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    private JobDescriptionResponse mapToResponse(JobDescription jobDescription) {
        return new JobDescriptionResponse(
                jobDescription.getId(),
                jobDescription.getTitle(),
                jobDescription.getDescription(),
                jobDescription.getRequirements(),
                jobDescription.getResponsibilities(),
                jobDescription.getDepartment(),
                jobDescription.getLocation(),
                jobDescription.getSalaryRange(),
                jobDescription.getExperienceLevel(),
                jobDescription.getCreatedAt(),
                jobDescription.getUpdatedAt(),
                jobDescription.getCreatedBy() != null && jobDescription.getCreatedBy().getPerson() != null ?
                    jobDescription.getCreatedBy().getPerson().getFirstName() + " " + jobDescription.getCreatedBy().getPerson().getLastName() :
                    "Unknown"
        );
    }
}
