import React, { useState } from 'react';
import { Box, Typography, TextField, Button, Paper, Grid, Chip, MenuItem, Tabs, Tab, LinearProgress, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';
import { Delete, Edit } from '@mui/icons-material';

const ITContainer = styled(Box)(({ theme }) => ({
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, #1a2525 0%, #263238 100%)'
    : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  minHeight: '100vh',
  display: 'flex',
}));

const SectionCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: '12px',
  background: theme.palette.mode === 'dark' ? '#263238' : '#fff',
  boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px rgba(33, 150, 243, 0.3)' : '0 2px 10px rgba(0, 0, 0, 0.1)',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
}));

const IT = () => {
  const [tabValue, setTabValue] = useState(0);

  // 1. Ticket Management State
  const [tickets, setTickets] = useState([]);
  const [newTicket, setNewTicket] = useState({ description: '', urgency: 'Medium' });

  // 2. Asset Management State
  const [assets, setAssets] = useState([]);
  const [newAsset, setNewAsset] = useState({ serialNumber: '', assignedTo: '', status: 'In Use' });

  // 3. Network Monitoring State (Mock Data)
  const [networkStatus, setNetworkStatus] = useState({ servers: 'Up', bandwidth: 50 });

  // 4. Software Deployment State
  const [software, setSoftware] = useState([]);
  const [newSoftware, setNewSoftware] = useState({ name: '', version: '' });

  // 5. User Account Management State
  const [users, setUsers] = useState([]);
  const [newUser, setNewUser] = useState({ email: '', role: 'User' });

  // 6. Backup and Recovery State
  const [backups, setBackups] = useState([]);
  const [newBackup, setNewBackup] = useState({ system: '', schedule: 'Daily' });

  // 7. Security Incident State
  const [incidents, setIncidents] = useState([]);
  const [newIncident, setNewIncident] = useState({ description: '', severity: 'Medium' });

  // Handlers for each functionality
  const handleAddTicket = () => {
    if (newTicket.description) {
      setTickets([...tickets, { id: Date.now(), ...newTicket, status: 'Open', createdAt: new Date() }]);
      setNewTicket({ description: '', urgency: 'Medium' });
    }
  };

  const handleAddAsset = () => {
    if (newAsset.serialNumber && newAsset.assignedTo) {
      setAssets([...assets, { id: Date.now(), ...newAsset }]);
      setNewAsset({ serialNumber: '', assignedTo: '', status: 'In Use' });
    }
  };

  const handleAddSoftware = () => {
    if (newSoftware.name && newSoftware.version) {
      setSoftware([...software, { id: Date.now(), ...newSoftware }]);
      setNewSoftware({ name: '', version: '' });
    }
  };

  const handleAddUser = () => {
    if (newUser.email) {
      setUsers([...users, { id: Date.now(), ...newUser, active: true }]);
      setNewUser({ email: '', role: 'User' });
    }
  };

  const handleAddBackup = () => {
    if (newBackup.system) {
      setBackups([...backups, { id: Date.now(), ...newBackup, lastRun: new Date(), status: 'Success' }]);
      setNewBackup({ system: '', schedule: 'Daily' });
    }
  };

  const handleAddIncident = () => {
    if (newIncident.description) {
      setIncidents([...incidents, { id: Date.now(), ...newIncident, reportedAt: new Date() }]);
      setNewIncident({ description: '', severity: 'Medium' });
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <ITContainer>
      <Sidebar />
      <Box sx={{ flexGrow: 1, marginLeft: '250px', p: 3 }}>
        <Navbar />
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" sx={{ mb: 3, color: theme => theme.palette.text.primary }}>
            IT Department Dashboard
          </Typography>

          {/* Tabs for Navigation */}
          <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
            <Tab label="Tickets" />
            <Tab label="Assets" />
            <Tab label="Network" />
            <Tab label="Software" />
            <Tab label="Users" />
            <Tab label="Backups" />
            <Tab label="Incidents" />
          </Tabs>

          {/* Tab Content */}
          {tabValue === 0 && (
            <Box>
              <SectionCard sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Submit IT Ticket</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={8}>
                    <TextField label="Issue Description" value={newTicket.description} onChange={(e) => setNewTicket({ ...newTicket, description: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <TextField select label="Urgency" value={newTicket.urgency} onChange={(e) => setNewTicket({ ...newTicket, urgency: e.target.value })} fullWidth>
                      <MenuItem value="Low">Low</MenuItem>
                      <MenuItem value="Medium">Medium</MenuItem>
                      <MenuItem value="High">High</MenuItem>
                    </TextField>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Button variant="contained" onClick={handleAddTicket} fullWidth>Submit</Button>
                  </Grid>
                </Grid>
              </SectionCard>
              <Typography variant="h6" sx={{ mb: 2 }}>Open Tickets</Typography>
              <Grid container spacing={2}>
                {tickets.map((ticket) => (
                  <Grid item xs={12} sm={6} md={4} key={ticket.id}>
                    <SectionCard>
                      <Typography variant="subtitle1">{ticket.description}</Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Chip label={ticket.urgency} color={ticket.urgency === 'High' ? 'error' : ticket.urgency === 'Medium' ? 'warning' : 'success'} />
                        <Chip label={ticket.status} color="primary" />
                      </Box>
                      <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                        Created: {new Date(ticket.createdAt).toLocaleString()}
                      </Typography>
                    </SectionCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {tabValue === 1 && (
            <Box>
              <SectionCard sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Add New Asset</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField label="Serial Number" value={newAsset.serialNumber} onChange={(e) => setNewAsset({ ...newAsset, serialNumber: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField label="Assigned To" value={newAsset.assignedTo} onChange={(e) => setNewAsset({ ...newAsset, assignedTo: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <TextField select label="Status" value={newAsset.status} onChange={(e) => setNewAsset({ ...newAsset, status: e.target.value })} fullWidth>
                      <MenuItem value="In Use">In Use</MenuItem>
                      <MenuItem value="In Repair">In Repair</MenuItem>
                      <MenuItem value="Retired">Retired</MenuItem>
                    </TextField>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Button variant="contained" onClick={handleAddAsset} fullWidth>Add</Button>
                  </Grid>
                </Grid>
              </SectionCard>
              <Typography variant="h6" sx={{ mb: 2 }}>Asset List</Typography>
              <Grid container spacing={2}>
                {assets.map((asset) => (
                  <Grid item xs={12} sm={6} md={4} key={asset.id}>
                    <SectionCard>
                      <Typography variant="subtitle1">Serial: {asset.serialNumber}</Typography>
                      <Typography variant="body2">Assigned: {asset.assignedTo}</Typography>
                      <Chip label={asset.status} color={asset.status === 'In Use' ? 'success' : asset.status === 'In Repair' ? 'warning' : 'error'} sx={{ mt: 1 }} />
                    </SectionCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {tabValue === 2 && (
            <Box>
              <SectionCard>
                <Typography variant="h6" sx={{ mb: 2 }}>Network Status</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Servers: <Chip label={networkStatus.servers} color={networkStatus.servers === 'Up' ? 'success' : 'error'} /></Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Bandwidth Usage</Typography>
                    <LinearProgress variant="determinate" value={networkStatus.bandwidth} sx={{ mt: 1 }} />
                    <Typography variant="caption">{networkStatus.bandwidth}%</Typography>
                  </Grid>
                </Grid>
                <Button variant="outlined" sx={{ mt: 2 }}>Run Ping Test</Button>
              </SectionCard>
            </Box>
          )}

          {tabValue === 3 && (
            <Box>
              <SectionCard sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Deploy Software</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={5}>
                    <TextField label="Software Name" value={newSoftware.name} onChange={(e) => setNewSoftware({ ...newSoftware, name: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={5}>
                    <TextField label="Version" value={newSoftware.version} onChange={(e) => setNewSoftware({ ...newSoftware, version: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Button variant="contained" onClick={handleAddSoftware} fullWidth>Deploy</Button>
                  </Grid>
                </Grid>
              </SectionCard>
              <Typography variant="h6" sx={{ mb: 2 }}>Deployed Software</Typography>
              <Grid container spacing={2}>
                {software.map((sw) => (
                  <Grid item xs={12} sm={6} md={4} key={sw.id}>
                    <SectionCard>
                      <Typography variant="subtitle1">{sw.name}</Typography>
                      <Typography variant="body2">Version: {sw.version}</Typography>
                    </SectionCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {tabValue === 4 && (
            <Box>
              <SectionCard sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Add User</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField label="Email" value={newUser.email} onChange={(e) => setNewUser({ ...newUser, email: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField select label="Role" value={newUser.role} onChange={(e) => setNewUser({ ...newUser, role: e.target.value })} fullWidth>
                      <MenuItem value="User">User</MenuItem>
                      <MenuItem value="Admin">Admin</MenuItem>
                    </TextField>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Button variant="contained" onClick={handleAddUser} fullWidth>Add</Button>
                  </Grid>
                </Grid>
              </SectionCard>
              <Typography variant="h6" sx={{ mb: 2 }}>User Accounts</Typography>
              <Grid container spacing={2}>
                {users.map((user) => (
                  <Grid item xs={12} sm={6} md={4} key={user.id}>
                    <SectionCard>
                      <Typography variant="subtitle1">{user.email}</Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Chip label={user.role} color={user.role === 'Admin' ? 'primary' : 'default'} />
                        <Chip label={user.active ? 'Active' : 'Inactive'} color={user.active ? 'success' : 'error'} />
                      </Box>
                    </SectionCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {tabValue === 5 && (
            <Box>
              <SectionCard sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Schedule Backup</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField label="System" value={newBackup.system} onChange={(e) => setNewBackup({ ...newBackup, system: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <TextField select label="Schedule" value={newBackup.schedule} onChange={(e) => setNewBackup({ ...newBackup, schedule: e.target.value })} fullWidth>
                      <MenuItem value="Daily">Daily</MenuItem>
                      <MenuItem value="Weekly">Weekly</MenuItem>
                      <MenuItem value="Monthly">Monthly</MenuItem>
                    </TextField>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Button variant="contained" onClick={handleAddBackup} fullWidth>Schedule</Button>
                  </Grid>
                </Grid>
              </SectionCard>
              <Typography variant="h6" sx={{ mb: 2 }}>Backup Status</Typography>
              <Grid container spacing={2}>
                {backups.map((backup) => (
                  <Grid item xs={12} sm={6} md={4} key={backup.id}>
                    <SectionCard>
                      <Typography variant="subtitle1">{backup.system}</Typography>
                      <Typography variant="body2">Schedule: {backup.schedule}</Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Chip label={backup.status} color={backup.status === 'Success' ? 'success' : 'error'} />
                        <Typography variant="caption">Last Run: {new Date(backup.lastRun).toLocaleString()}</Typography>
                      </Box>
                    </SectionCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {tabValue === 6 && (
            <Box>
              <SectionCard sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Report Security Incident</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={8}>
                    <TextField label="Incident Description" value={newIncident.description} onChange={(e) => setNewIncident({ ...newIncident, description: e.target.value })} fullWidth />
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <TextField select label="Severity" value={newIncident.severity} onChange={(e) => setNewIncident({ ...newIncident, severity: e.target.value })} fullWidth>
                      <MenuItem value="Low">Low</MenuItem>
                      <MenuItem value="Medium">Medium</MenuItem>
                      <MenuItem value="High">High</MenuItem>
                    </TextField>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Button variant="contained" onClick={handleAddIncident} fullWidth>Report</Button>
                  </Grid>
                </Grid>
              </SectionCard>
              <Typography variant="h6" sx={{ mb: 2 }}>Security Incidents</Typography>
              <Grid container spacing={2}>
                {incidents.map((incident) => (
                  <Grid item xs={12} sm={6} md={4} key={incident.id}>
                    <SectionCard>
                      <Typography variant="subtitle1">{incident.description}</Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Chip label={incident.severity} color={incident.severity === 'High' ? 'error' : incident.severity === 'Medium' ? 'warning' : 'success'} />
                      </Box>
                      <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                        Reported: {new Date(incident.reportedAt).toLocaleString()}
                      </Typography>
                    </SectionCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </Box>
      </Box>
    </ITContainer>
  );
};

export default IT;