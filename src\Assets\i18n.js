import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  en: {
    translation: {
      welcome: 'Welcome',
      home: 'Home',
      profile: 'Profile',
      settings: 'Settings',
      logout: 'Logout',
      hr: 'HR',
      finance: 'Finance',
      it: 'IT',
      fileManagement: 'File Management',
      sales: 'Sales',
      mail: 'Mail',
      calendar: 'Calendar',
      tasks: 'Tasks',
      dashboard: 'Dashboard',
      user_management: 'User Management',
      recent_activity: 'Recent Activity',
      sales_overview: 'Sales Overview',
      quick_actions: 'Quick Actions',
      system_status: 'System Status',
      overview: 'Overview',
      analytics: 'Analytics',
      actions_status: 'Actions & Status',
      total_users: 'Total Users',
      total_sales: 'Total Sales',
      pending_tasks: 'Pending Tasks',
      notifications: 'Notifications',
      search_users: 'Search Users',
      all_roles: 'All Roles',
      all_departments: 'All Departments',
      all_statuses: 'All Statuses',
      filter: 'Filter',
      add_user: 'Add User',
      edit_user: 'Edit User',
      name: 'Name',
      email: 'Email',
      role: 'Role',
      department: 'Department',
      status: 'Status',
      cancel: 'Cancel',
      save: 'Save',
      admin: 'Admin',
      editor: 'Editor',
      user: 'User',
      active: 'Active',
      inactive: 'Inactive',
      role_management: 'Role Management',
      department_overview: 'Department Overview',
      view: 'View',
      audit_log: 'Audit Log',
      user_added: 'Added user: {{name}}',
      user_updated: 'Updated user: {{name}}',
      user_deleted: 'Deleted user: {{name}}',
      select_action: 'Select Action',
      activate: 'Activate',
      deactivate: 'Deactivate',
      export: 'Export',
      apply: 'Apply',
      actions: 'Actions',
      no_users_found: 'No Users Found',
    },
  },
  es: {
    translation: {
      welcome: 'Bienvenido',
      home: 'Inicio',
      profile: 'Perfil',
      settings: 'Configuración',
      logout: 'Cerrar sesión',
      hr: 'Recursos Humanos',
      finance: 'Finanzas',
      it: 'TI',
      fileManagement: 'Gestión de Archivos',
      sales: 'Ventas',
      mail: 'Correo',
      calendar: 'Calendario',
      tasks: 'Tareas',
      dashboard: 'Tablero',
      user_management: 'Gestión de Usuarios',
      recent_activity: 'Actividad Reciente',
      sales_overview: 'Resumen de Ventas',
      quick_actions: 'Acciones Rápidas',
      system_status: 'Estado del Sistema',
      overview: 'Resumen',
      analytics: 'Análisis',
      actions_status: 'Acciones y Estado',
      total_users: 'Usuarios Totales',
      total_sales: 'Ventas Totales',
      pending_tasks: 'Tareas Pendientes',
      notifications: 'Notificaciones',
      search_users: 'Buscar Usuarios',
      all_roles: 'Todos los Roles',
      all_departments: 'Todos los Departamentos',
      all_statuses: 'Todos los Estados',
      filter: 'Filtrar',
      add_user: 'Agregar Usuario',
      edit_user: 'Editar Usuario',
      name: 'Nombre',
      email: 'Correo Electrónico',
      role: 'Rol',
      department: 'Departamento',
      status: 'Estado',
      cancel: 'Cancelar',
      save: 'Guardar',
      admin: 'Administrador',
      editor: 'Editor',
      user: 'Usuario',
      active: 'Activo',
      inactive: 'Inactivo',
      role_management: 'Gestión de Roles',
      department_overview: 'Resumen de Departamentos',
      view: 'Ver',
      audit_log: 'Registro de Auditoría',
      user_added: 'Usuario agregado: {{name}}',
      user_updated: 'Usuario actualizado: {{name}}',
      user_deleted: 'Usuario eliminado: {{name}}',
      select_action: 'Seleccionar Acción',
      activate: 'Activar',
      deactivate: 'Desactivar',
      export: 'Exportar',
      apply: 'Aplicar',
      actions: 'Acciones',
      no_users_found: 'No se encontraron usuarios',
    },
  },
  fr: {
    translation: {
      welcome: 'Bienvenue',
      home: 'Accueil',
      profile: 'Profil',
      settings: 'Paramètres',
      logout: 'Déconnexion',
      hr: 'Ressources Humaines',
      finance: 'Finance',
      it: 'Informatique',
      fileManagement: 'Gestion des Fichiers',
      sales: 'Ventes',
      mail: 'Courrier',
      calendar: 'Calendrier',
      tasks: 'Tâches',
      dashboard: 'Tableau de Bord',
      user_management: 'Gestion des Utilisateurs',
      recent_activity: 'Activité Récente',
      sales_overview: 'Aperçu des Ventes',
      quick_actions: 'Actions Rapides',
      system_status: 'État du Système',
      overview: 'Aperçu',
      analytics: 'Analytique',
      actions_status: 'Actions et État',
      total_users: 'Utilisateurs Totaux',
      total_sales: 'Ventes Totales',
      pending_tasks: 'Tâches en Attente',
      notifications: 'Notifications',
      search_users: 'Rechercher des Utilisateurs',
      all_roles: 'Tous les Rôles',
      all_departments: 'Tous les Départements',
      all_statuses: 'Tous les Statuts',
      filter: 'Filtrer',
      add_user: 'Ajouter un Utilisateur',
      edit_user: 'Modifier l’Utilisateur',
      name: 'Nom',
      email: 'Courriel',
      role: 'Rôle',
      department: 'Département',
      status: 'Statut',
      cancel: 'Annuler',
      save: 'Enregistrer',
      admin: 'Administrateur',
      editor: 'Éditeur',
      user: 'Utilisateur',
      active: 'Actif',
      inactive: 'Inactif',
      role_management: 'Gestion des Rôles',
      department_overview: 'Aperçu des Départements',
      view: 'Voir',
      audit_log: 'Journal d’Audit',
      user_added: 'Utilisateur ajouté : {{name}}',
      user_updated: 'Utilisateur mis à jour : {{name}}',
      user_deleted: 'Utilisateur supprimé : {{name}}',
      select_action: 'Sélectionner une Action',
      activate: 'Activer',
      deactivate: 'Désactiver',
      export: 'Exporter',
      apply: 'Appliquer',
      actions: 'Actions',
      no_users_found: 'Aucun utilisateur trouvé',
    },
  },
  ar: {
    translation: {
      welcome: 'مرحبًا',
      home: 'الصفحة الرئيسية',
      profile: 'الملف الشخصي',
      settings: 'الإعدادات',
      logout: 'تسجيل الخروج',
      hr: 'الموارد البشرية',
      finance: 'المالية',
      it: 'تكنولوجيا المعلومات',
      fileManagement: 'إدارة الملفات',
      sales: 'المبيعات',
      mail: 'البريد',
      calendar: 'التقويم',
      tasks: 'المهام',
      dashboard: 'لوحة التحكم',
      user_management: 'إدارة المستخدمين',
      recent_activity: 'النشاط الأخير',
      sales_overview: 'نظرة عامة على المبيعات',
      quick_actions: 'إجراءات سريعة',
      system_status: 'حالة النظام',
      overview: 'نظرة عامة',
      analytics: 'التحليلات',
      actions_status: 'الإجراءات والحالة',
      total_users: 'إجمالي المستخدمين',
      total_sales: 'إجمالي المبيعات',
      pending_tasks: 'المهام المعلقة',
      notifications: 'الإشعارات',
      search_users: 'البحث عن المستخدمين',
      all_roles: 'جميع الأدوار',
      all_departments: 'جميع الأقسام',
      all_statuses: 'جميع الحالات',
      filter: 'تصفية',
      add_user: 'إضافة مستخدم',
      edit_user: 'تعديل المستخدم',
      name: 'الاسم',
      email: 'البريد الإلكتروني',
      role: 'الدور',
      department: 'القسم',
      status: 'الحالة',
      cancel: 'إلغاء',
      save: 'حفظ',
      admin: 'مدير',
      editor: 'محرر',
      user: 'مستخدم',
      active: 'نشط',
      inactive: 'غير نشط',
      role_management: 'إدارة الأدوار',
      department_overview: 'نظرة عامة على الأقسام',
      view: 'عرض',
      audit_log: 'سجل التدقيق',
      user_added: 'تمت إضافة المستخدم: {{name}}',
      user_updated: 'تم تحديث المستخدم: {{name}}',
      user_deleted: 'تم حذف المستخدم: {{name}}',
      select_action: 'اختيار إجراء',
      activate: 'تفعيل',
      deactivate: 'إلغاء التفعيل',
      export: 'تصدير',
      apply: 'تطبيق',
      actions: 'إجراءات',
      no_users_found: 'لم يتم العثور على مستخدمين',
    },
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;