import axios from 'axios';

// Create an Axios instance with base configuration
const apiClient = axios.create({
  baseURL: 'http://localhost:8088',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to headers for authenticated requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => Promise.reject(error));

/**
 * Adds a new user to the system.
 * @param {Object} userDTO - The user data { email, password, roleId, person }.
 * @returns {Promise<Object>} The created user response.
 * @throws {Object} Error response if the request fails.
 */
export const addUser = async (userDTO) => {
  try {
    const response = await apiClient.post('/api/users/user/', userDTO);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};

/**
 * Updates an existing user by ID.
 * @param {Object} userDTO - The updated user data { email, password, roleId, person }.
 * @param {string} id - The UUID of the user to update.
 * @returns {Promise<Object>} The updated user response.
 * @throws {Object} Error response if the request fails.
 */
export const updateUser = async (userDTO, id) => {
  try {
    const response = await apiClient.put(`/api/users/updateById/${id}`, userDTO);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};

/**
 * Deletes a user by ID.
 * @param {string} id - The UUID of the user to delete.
 * @returns {Promise<void>} No content on success.
 * @throws {Object} Error response if the request fails.
 */
export const deleteUser = async (id) => {
  try {
    await apiClient.delete(`/api/users/deleteById/${id}`);
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};

/**
 * Retrieves a user by ID.
 * @param {string} id - The UUID of the user to find.
 * @returns {Promise<Object>} The user response.
 * @throws {Object} Error response if the request fails.
 */
export const findUserById = async (id) => {
  try {
    const response = await apiClient.get(`/api/users/findById/${id}`);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};

/**
 * Retrieves all users.
 * @returns {Promise<Array<Object>>} List of user responses.
 * @throws {Object} Error response if the request fails.
 */
export const findAllUsers = async () => {
  try {
    const response = await apiClient.get('/api/users/findAll/');
    return response.data;
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};

/**
 * Retrieves a user by email.
 * @param {string} email - The email of the user to find.
 * @returns {Promise<Object>} The user object.
 * @throws {Object} Error response if the request fails.
 */
export const findUserByEmail = async (email) => {
  try {
    const response = await apiClient.get(`/api/users/findByEmail/1/${email}`);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw error.response.data;
    }
    throw { error: 'Network error or server is unreachable' };
  }
};

export const addPerson = async (personData) => {
  const response = await fetch('http://localhost:8088/api/persons', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // Add authorization header if needed
    },
    body: JSON.stringify(personData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to add person');
  }

  return response.json();
};

export const fetchRoles = async () => {
  const response = await fetch('http://localhost:8088/api/roles', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      // Add authorization header if needed
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch roles');
  }

  return response.json();
};