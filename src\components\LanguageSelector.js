import React, { useState } from 'react';
import { Icon<PERSON>utton, Menu, MenuItem, Typography, Box, styled } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';

const LanguageMenu = styled(Menu)(({ theme }) => ({
  '& .MuiPaper-root': {
    width: '280px',
    borderRadius: '16px',
    background: theme.palette.mode === 'dark' 
      ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' 
      : '#ffffff',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 6px 25px rgba(0, 0, 0, 0.7)' 
      : '0 6px 25px rgba(0, 0, 0, 0.2)',
    border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
    overflow: 'hidden',
  },
}));

const LanguageItem = styled(MenuItem)(({ theme }) => ({
  padding: theme.spacing(1.5, 2.5),
  transition: 'all 0.3s ease-in-out',
  backgroundColor: theme.palette.mode === 'dark' ? '#2e3b3e' : '#fafafa',
  margin: theme.spacing(0.5, 1),
  borderRadius: '8px',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#455a64' : '#e3f2fd',
    transform: 'translateY(-2px)',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 4px 15px rgba(33, 150, 243, 0.5)' 
      : '0 4px 15px rgba(33, 150, 243, 0.3)',
  },
}));

const FlagIcon = styled('img')({
  width: '28px',
  height: '28px',
  borderRadius: '50%',
  objectFit: 'cover',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'rotate(15deg) scale(1.1)',
  },
});

const LanguageSelector = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const { i18n } = useTranslation();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  const languages = [
    { code: 'en', name: 'English', flag: 'https://flagcdn.com/us.svg' },
    { code: 'es', name: 'Spanish', flag: 'https://flagcdn.com/es.svg' },
    { code: 'fr', name: 'French', flag: 'https://flagcdn.com/fr.svg' },
    { code: 'ar', name: 'Arabic', flag: 'https://flagcdn.com/ae.svg' },
  ];

  const handleOpen = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    handleClose();
  };

  return (
    <>
      <IconButton color="inherit" onClick={handleOpen}>
        <LanguageIcon sx={{ fontSize: '1.5rem' }} />
      </IconButton>

      <LanguageMenu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Box
          sx={{
            p: 2,
            background: isDarkMode 
              ? 'linear-gradient(45deg, #37474f 0%, #455a64 100%)' 
              : 'linear-gradient(45deg, #3f51b5 0%, #1976d2 100%)',
            borderBottom: `1px solid ${isDarkMode ? '#455a64' : '#e0e0e0'}`,
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 700,
              color: '#fff',
              textAlign: 'center',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            }}
          >
            Select Language
          </Typography>
        </Box>
        {languages.map((language) => (
          <LanguageItem key={language.code} onClick={() => handleLanguageChange(language.code)}>
            <FlagIcon src={language.flag} alt={language.name} sx={{ mr: 2 }} />
            <Typography variant="body1" sx={{ fontWeight: 600, color: isDarkMode ? '#e0e0e0' : '#1e3c72', flexGrow: 1 }}>
              {language.name}
            </Typography>
            {i18n.language === language.code && (
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: isDarkMode ? '#90caf9' : '#2196f3',
                  ml: 1,
                }}
              />
            )}
          </LanguageItem>
        ))}
      </LanguageMenu>
    </>
  );
};

export default LanguageSelector;