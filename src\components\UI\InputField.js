import React from 'react';
import TextField from '@mui/material/TextField'; // Material-UI TextField
import 'bootstrap/dist/css/bootstrap.min.css'; // Bootstrap CSS

const InputField = ({ label, placeholder, value, onChange, error, helperText, type = 'text', className = '' }) => {
  return (
    <div className={`mb-3 ${className}`}>
      <TextField
        label={label}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        type={type}
        error={error}
        helperText={helperText}
        fullWidth
        variant="outlined"
      />
    </div>
  );
};

export default InputField;