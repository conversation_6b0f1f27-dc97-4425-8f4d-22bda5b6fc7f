import React, { useState } from 'react';
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Paper,
  useTheme,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';
import { useTheme as useAppTheme } from '../Assets/ThemeContext';
import {
  ChevronLeft,
  ChevronRight,
  Today,
  Search,
} from '@mui/icons-material';

const localizer = momentLocalizer(moment);

const CalendarContainer = styled(Box)(({ theme }) => ({
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, #1a2525 0%, #263238 100%)' // Matches App.js dark mode background
    : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  minHeight: '100vh',
  display: 'flex',
  position: 'relative',
  '&::before': {
    content: theme.palette.mode === 'dark' ? '""' : 'none',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, rgba(0, 0, 0, 0) 70%)',
    zIndex: 0,
    pointerEvents: 'none',
  },
}));

const HeaderBar = styled(Paper)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2),
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' // Matches MuiCard dark mode
    : 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  borderRadius: '16px',
  marginBottom: theme.spacing(2),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 8px 25px rgba(33, 150, 243, 0.3)' // Matches MuiCard hover shadow
    : '0 4px 20px rgba(0, 0, 0, 0.1)',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : '1px solid #e0e0e0', // Matches App.js border
}));

const EventChip = styled(Chip)(({ theme, category }) => ({
  background: category === 'Meeting' ? '#4caf50' : category === 'Task' ? '#2196f3' : category === 'Reminder' ? '#ff9800' : '#9c27b0',
  color: '#fff',
  marginRight: theme.spacing(1),
  fontWeight: 600,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-3px)', // Matches MuiButton hover
    boxShadow: theme.palette.mode === 'dark'
      ? '0 6px 20px rgba(33, 150, 243, 0.5)' // Matches MuiButton hover shadow
      : '0 2px 8px rgba(0, 0, 0, 0.2)',
  },
}));

const CalendarComponent = () => {
  const { isDarkMode } = useAppTheme();
  const theme = useTheme();
  const [events, setEvents] = useState([
    { id: 1, title: 'Interview with Alice', start: new Date(2023, 9, 15, 10, 0), end: new Date(2023, 9, 15, 11, 0), category: 'Meeting', priority: 'Medium', reminder: false },
    { id: 2, title: 'Code Review', start: new Date(2023, 9, 16, 14, 0), end: new Date(2023, 9, 16, 15, 0), category: 'Task', priority: 'High', reminder: true },
  ]);
  const [openDialog, setOpenDialog] = useState(false);
  const [newEvent, setNewEvent] = useState({ title: '', start: '', end: '', category: 'Meeting', priority: 'Medium', reminder: false });
  const [date, setDate] = useState(new Date());
  const [view, setView] = useState('week');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('All');
  const [miniCalDate, setMiniCalDate] = useState(new Date());

  const handleSelectSlot = (slotInfo) => {
    setNewEvent({ ...newEvent, start: slotInfo.start, end: slotInfo.end });
    setOpenDialog(true);
  };

  const handleAddEvent = () => {
    if (newEvent.title && newEvent.start && newEvent.end) {
      setEvents([...events, { ...newEvent, id: events.length + 1 }]);
      setOpenDialog(false);
      setNewEvent({ title: '', start: '', end: '', category: 'Meeting', priority: 'Medium', reminder: false });
    }
  };

  const handleEventResize = (data) => {
    const { start, end } = data;
    setEvents((prev) => prev.map((ev) => (ev.id === data.event.id ? { ...ev, start, end } : ev)));
  };

  const handleEventDrop = (data) => {
    const { start, end } = data;
    setEvents((prev) => prev.map((ev) => (ev.id === data.event.id ? { ...ev, start, end } : ev)));
  };

  const handleNavigate = (action) => {
    let newDate = new Date(date);
    switch (action) {
      case 'PREV':
        if (view === 'day') newDate.setDate(date.getDate() - 1);
        else if (view === 'week') newDate.setDate(date.getDate() - 7);
        else newDate.setMonth(date.getMonth() - 1);
        break;
      case 'NEXT':
        if (view === 'day') newDate.setDate(date.getDate() + 1);
        else if (view === 'week') newDate.setDate(date.getDate() + 7);
        else newDate.setMonth(date.getMonth() + 1);
        break;
      case 'TODAY':
        newDate = new Date();
        break;
      default:
        break;
    }
    setDate(newDate);
    setMiniCalDate(newDate);
  };

  const handleViewChange = (newView) => setView(newView);

  const filteredEvents = events.filter((event) => {
    const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'All' || event.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  const eventStyleGetter = (event) => {
    const backgroundColor = event.category === 'Meeting' ? '#4caf50' : event.category === 'Task' ? '#2196f3' : event.category === 'Reminder' ? '#ff9800' : '#9c27b0';
    return {
      style: {
        backgroundColor,
        borderRadius: '8px',
        opacity: 0.9,
        color: '#fff',
        border: event.priority === 'High' ? '2px solid #d32f2f' : 'none',
        boxShadow: isDarkMode ? '0 6px 20px rgba(33, 150, 243, 0.5)' : '0 2px 8px rgba(0, 0, 0, 0.2)', // Matches hover shadow
        transition: 'all 0.3s ease',
      },
    };
  };

  const calendarStyle = {
    height: 700,
    backgroundColor: isDarkMode ? '#263238' : '#fff', // Matches paper background
    color: isDarkMode ? '#e0e0e0' : '#333', // Matches text.primary
    borderRadius: '16px',
    padding: theme.spacing(2),
    boxShadow: isDarkMode ? '0 8px 25px rgba(33, 150, 243, 0.3)' : '0 4px 20px rgba(0, 0, 0, 0.1)', // Matches MuiCard
    border: isDarkMode ? '1px solid #455a64' : 'none', // Matches dark mode border
  };

  return (
    <CalendarContainer>
      <Sidebar />
      <Box sx={{ flexGrow: 1, marginLeft: '250px', p: 3, position: 'relative', zIndex: 1 }}>
        <Navbar />
  
        <HeaderBar>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton onClick={() => handleNavigate('PREV')} sx={{ color: isDarkMode ? '#90caf9' : 'primary.main' }}>
              <ChevronLeft />
            </IconButton>
            <IconButton onClick={() => handleNavigate('NEXT')} sx={{ color: isDarkMode ? '#90caf9' : 'primary.main' }}>
              <ChevronRight />
            </IconButton>
            <Button variant="contained" startIcon={<Today />} onClick={() => handleNavigate('TODAY')}>
              Today
            </Button>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel sx={{ color: isDarkMode ? '#e0e0e0' : '#333' }}>View</InputLabel>
              <Select value={view} onChange={(e) => handleViewChange(e.target.value)} sx={{ color: isDarkMode ? '#e0e0e0' : '#333' }}>
                <MenuItem value="day">Day</MenuItem>
                <MenuItem value="week">Week</MenuItem>
                <MenuItem value="month">Month</MenuItem>
              </Select>
            </FormControl>
          </Box>
  
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TextField
              size="small"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{ startAdornment: <Search fontSize="small" sx={{ color: isDarkMode ? '#b0bec5' : '#666' }} /> }}
              sx={{ input: { color: isDarkMode ? '#e0e0e0' : '#333' } }}
            />
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel sx={{ color: isDarkMode ? '#e0e0e0' : '#333' }}>Category</InputLabel>
              <Select value={filterCategory} onChange={(e) => setFilterCategory(e.target.value)} sx={{ color: isDarkMode ? '#e0e0e0' : '#333' }}>
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="Meeting">Meeting</MenuItem>
                <MenuItem value="Task">Task</MenuItem>
                <MenuItem value="Reminder">Reminder</MenuItem>
              </Select>
            </FormControl>
            <Button variant="outlined" component="label">
              Import
              <input hidden type="file" accept=".json"  />
            </Button>
            <Button variant="contained" >
              Export
            </Button>
          </Box>
        </HeaderBar>
  
        <Box sx={calendarStyle}>
          <Calendar
            localizer={localizer}
            events={filteredEvents}
            startAccessor="start"
            endAccessor="end"
            selectable
            resizable
            defaultView={view}
            view={view}
            onView={setView}
            date={date}
            onNavigate={setDate}
            onSelectSlot={handleSelectSlot}
            onEventDrop={handleEventDrop}
            onEventResize={handleEventResize}
            style={{ height: '100%' }}
            eventPropGetter={eventStyleGetter}
          />
        </Box>
  
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} PaperProps={{ sx: { background: isDarkMode ? '#263238' : '#fff', border: isDarkMode ? '1px solid #455a64' : 'none' } }}>
          <DialogTitle sx={{ color: isDarkMode ? '#e0e0e0' : '#333' }}>Add New Event</DialogTitle>
          <DialogContent>
            <TextField fullWidth label="Title" margin="dense" value={newEvent.title} onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })} sx={{ input: { color: isDarkMode ? '#e0e0e0' : '#333' } }} />
            <TextField fullWidth label="Start Time" type="datetime-local" margin="dense" value={moment(newEvent.start).format('YYYY-MM-DDTHH:mm')} onChange={(e) => setNewEvent({ ...newEvent, start: new Date(e.target.value) })} sx={{ input: { color: isDarkMode ? '#e0e0e0' : '#333' } }} />
            <TextField fullWidth label="End Time" type="datetime-local" margin="dense" value={moment(newEvent.end).format('YYYY-MM-DDTHH:mm')} onChange={(e) => setNewEvent({ ...newEvent, end: new Date(e.target.value) })} sx={{ input: { color: isDarkMode ? '#e0e0e0' : '#333' } }} />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)} sx={{ color: isDarkMode ? '#b0bec5' : '#666' }}>Cancel</Button>
            <Button onClick={handleAddEvent} variant="contained">Add</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </CalendarContainer>
  );
};

export default CalendarComponent;