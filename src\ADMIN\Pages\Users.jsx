import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Chip,
  TextField,
  InputAdornment,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  Badge,
  Tooltip,
  Pagination,
  Stack,
  Card,
  CardContent,
  Grid,
  useTheme,
  styled,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  Search,
  Add,
  MoreVert,
  Edit,
  Delete,
  FilterList,
  Refresh,
  CheckCircle,
  Cancel,
  Lock,
  LockOpen,
  PersonAdd,
  CloudDownload,
  Sort,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
} from '@mui/icons-material';
import { addUser, findAllUsers, updateUser, deleteUser } from '../API/userService';

const StatusBadge = styled(Badge)(({ theme }) => ({
  '& .MuiBadge-badge': {
    right: 8,
    top: 8,
    border: `2px solid ${theme.palette.background.paper}`,
    padding: '0 4px',
  },
}));

const SmallAvatar = styled(Avatar)(({ theme }) => ({
  width: 22,
  height: 22,
  border: `2px solid ${theme.palette.background.paper}`,
}));

const Users = () => {
  const theme = useTheme();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    roleId: '',
    person: {
      firstName: '',
      lastName: '',
      birthDate: '',
      phoneNumber: '',
      personType: 'EMPLOYEE',
      address: {
        streetName: '',
        zipCode: '',
        state: '',
        town: '',
      },
    },
  });
  const [formErrors, setFormErrors] = useState({});
  const [page, setPage] = useState(1);
  const usersPerPage = 5;
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await findAllUsers();
      console.log('Fetched users:', data);
      setUsers(data);
    } catch (err) {
      setError(err.error || 'Failed to fetch users');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!newUser.email) errors.email = 'Email is required';
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) errors.email = 'Invalid email format';
    if (!newUser.password) errors.password = 'Password is required';
    if (!newUser.roleId) errors.roleId = 'Role ID is required';
    if (!newUser.person.firstName) errors.firstName = 'First name is required';
    if (!newUser.person.lastName) errors.lastName = 'Last name is required';
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddUser = async () => {
    if (!validateForm()) {
      setError('Please fix the errors in the form');
      setSnackbarOpen(true);
      return;
    }

    setLoading(true);
    try {
      const response = await addUser(newUser);
      setUsers([...users, response]);
      setOpenAddDialog(false);
      setNewUser({
        email: '',
        password: '',
        roleId: '',
        person: {
          firstName: '',
          lastName: '',
          birthDate: '',
          phoneNumber: '',
          personType: 'EMPLOYEE',
          address: {
            streetName: '',
            zipCode: '',
            state: '',
            town: '',
          },
        },
      });
      setError(null);
      setSnackbarOpen(true);
    } catch (err) {
      setError(err.message || err.error || 'Failed to add user');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async () => {
    if (selectedUser) {
      setLoading(true);
      try {
        const userDTO = { ...selectedUser, roleId: selectedUser.role };
        const response = await updateUser(userDTO, selectedUser.userId);
        setUsers(users.map(u => u.userId === response.userId ? response : u));
        handleClose();
        setError(null);
        setSnackbarOpen(true);
      } catch (err) {
        setError(err.message || err.error || 'Failed to update user');
        setSnackbarOpen(true);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleDeleteUser = async () => {
    if (selectedUser) {
      setLoading(true);
      try {
        await deleteUser(selectedUser.userId);
        setUsers(users.filter(u => u.userId !== selectedUser.userId));
        handleClose();
        setError(null);
        setSnackbarOpen(true);
      } catch (err) {
        setError(err.message || err.error || 'Failed to delete user');
        setSnackbarOpen(true);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleClick = (event, user) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const paginatedUsers = users.slice((page - 1) * usersPerPage, page * usersPerPage);

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
    setError(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
      <Typography
        variant="h4"
        fontWeight="bold"
        sx={{
          color: '#2c3e50',
          textShadow: '1px 1px 3px rgba(0, 0, 0, 0.3)',
          letterSpacing: 1.2,
        }}
      >
        User Management
      </Typography>
        <Box>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={fetchUsers}
            sx={{ mr: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PersonAdd />}
            onClick={() => setOpenAddDialog(true)}
          >
            Add User
          </Button>
        </Box>
      </Box>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <CircularProgress />
        </Box>
      )}
      {!loading && (
        <>
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: '100%', boxShadow: theme.shadows[3] }}>
                <CardContent>
                  <Typography variant="subtitle2" color="text.secondary">
                    Total Users
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {users.length}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <ArrowUpwardIcon color="success" fontSize="small" />
                    <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                      12% from last month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Card sx={{ mb: 3, boxShadow: theme.shadows[3] }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Search users..."
                    variant="outlined"
                    size="small"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button startIcon={<FilterList />} sx={{ mr: 2 }}>
                    Filters
                  </Button>
                  <Button startIcon={<Sort />} sx={{ mr: 2 }}>
                    Sort
                  </Button>
                  <Button startIcon={<CloudDownload />}>
                    Export
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ boxShadow: theme.shadows[3] }}>
            <TableContainer>
              <Table sx={{ minWidth: 800 }}>
                <TableHead sx={{ backgroundColor: theme.palette.grey[100] }}>
                  <TableRow>
                    <TableCell>User ID</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Person ID</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedUsers.map((user) => (
                    <TableRow hover key={user.userId}>
                      <TableCell>
                        <Typography variant="body2">
                          {user.userId || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {user.email || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.role || 'N/A'}
                          color={
                            user.role === 'ADMIN'
                              ? 'primary'
                              : user.role === 'USER'
                              ? 'secondary'
                              : 'default'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {user.personId || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="Edit">
                          <IconButton
                            color="primary"
                            size="small"
                            sx={{ mr: 1 }}
                            onClick={() => handleUpdateUser(user)}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="More options">
                          <IconButton
                            size="small"
                            onClick={(e) => handleClick(e, user)}
                          >
                            <MoreVert fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Card>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
            <Typography variant="body2" color="text.secondary">
              Showing {(page - 1) * usersPerPage + 1} to {Math.min(page * usersPerPage, users.length)} of {users.length} entries
            </Typography>
            <Pagination
              count={Math.ceil(users.length / usersPerPage)}
              page={page}
              onChange={handleChangePage}
              color="primary"
            />
          </Box>
        </>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleUpdateUser}>
          <Edit fontSize="small" sx={{ mr: 1 }} /> Edit Profile
        </MenuItem>
        <MenuItem onClick={handleDeleteUser} sx={{ color: 'error.main' }}>
          <Delete fontSize="small" sx={{ mr: 1 }} /> Delete User
        </MenuItem>
      </Menu>

      <Dialog open={openAddDialog} onClose={() => setOpenAddDialog(false)}>
        <DialogTitle>Add New User</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Email"
            fullWidth
            value={newUser.email}
            onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
            error={!!formErrors.email}
            helperText={formErrors.email}
          />
          <TextField
            margin="dense"
            label="Password"
            type="password"
            fullWidth
            value={newUser.password}
            onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
            error={!!formErrors.password}
            helperText={formErrors.password}
          />
          <TextField
            margin="dense"
            label="Role ID"
            fullWidth
            value={newUser.roleId}
            onChange={(e) => setNewUser({ ...newUser, roleId: e.target.value })}
            error={!!formErrors.roleId}
            helperText={formErrors.roleId}
          />
          <TextField
            margin="dense"
            label="First Name"
            fullWidth
            value={newUser.person.firstName}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, firstName: e.target.value } })}
            error={!!formErrors.firstName}
            helperText={formErrors.firstName}
          />
          <TextField
            margin="dense"
            label="Last Name"
            fullWidth
            value={newUser.person.lastName}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, lastName: e.target.value } })}
            error={!!formErrors.lastName}
            helperText={formErrors.lastName}
          />
          <TextField
            margin="dense"
            label="Birth Date"
            type="date"
            fullWidth
            value={newUser.person.birthDate}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, birthDate: e.target.value } })}
            InputLabelProps={{ shrink: true }}
          />
          <TextField
            margin="dense"
            label="Phone Number"
            fullWidth
            value={newUser.person.phoneNumber}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, phoneNumber: e.target.value } })}
          />
          <TextField
            margin="dense"
            label="Street Name"
            fullWidth
            value={newUser.person.address.streetName}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, address: { ...newUser.person.address, streetName: e.target.value } } })}
          />
          <TextField
            margin="dense"
            label="Zip Code"
            fullWidth
            value={newUser.person.address.zipCode}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, address: { ...newUser.person.address, zipCode: e.target.value } } })}
          />
          <TextField
            margin="dense"
            label="State"
            fullWidth
            value={newUser.person.address.state}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, address: { ...newUser.person.address, state: e.target.value } } })}
          />
          <TextField
            margin="dense"
            label="Town"
            fullWidth
            value={newUser.person.address.town}
            onChange={(e) => setNewUser({ ...newUser, person: { ...newUser.person, address: { ...newUser.person.address, town: e.target.value } } })}
          />
          {error && <Typography color="error">{error}</Typography>}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAddDialog(false)}>Cancel</Button>
          <Button onClick={handleAddUser} variant="contained" color="primary" disabled={loading}>
            {loading ? 'Adding...' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={error ? 'error' : 'success'} sx={{ width: '100%' }}>
          {error || 'Operation successful!'}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Users;