import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Typography,
  TextField,
  Button,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  useMediaQuery,
  IconButton,
  Tooltip,
  Collapse,
} from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
import {
  Add as AddIcon,
  AttachFile as AttachFileIcon,
  Reply as ReplyIcon,
  Forward as ForwardIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';

const MailContainer = styled(Box)(({ theme }) => ({
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, #1a2525 0%, #2e3b3e 100%)'
    : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  minHeight: '100vh',
  display: 'flex',
  position: 'relative',
  '&::before': theme.palette.mode === 'dark' && {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, rgba(0, 0, 0, 0) 70%)',
    zIndex: 0,
    pointerEvents: 'none',
  },
}));

const MailListContainer = styled(Paper)(({ theme }) => ({
  width: '30%',
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)'
    : 'rgba(255, 255, 255, 0.95)',
  borderRadius: '16px',
  margin: theme.spacing(2),
  overflow: 'hidden',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 15px rgba(0, 0, 0, 0.5)'
    : '0 4px 15px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  [theme.breakpoints.down('md')]: {
    width: '100%',
    margin: theme.spacing(1),
  },
}));

const MailPreviewContainer = styled(Paper)(({ theme }) => ({
  flexGrow: 1,
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)'
    : 'rgba(255, 255, 255, 0.95)',
  borderRadius: '16px',
  margin: theme.spacing(2),
  padding: theme.spacing(3),
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 15px rgba(0, 0, 0, 0.5)'
    : '0 4px 15px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  [theme.breakpoints.down('md')]: {
    margin: theme.spacing(1),
  },
}));

const StyledListItem = styled(ListItem)(({ theme }) => ({
  borderRadius: '8px',
  marginBottom: theme.spacing(1),
  padding: theme.spacing(1.5),
  transition: 'all 0.3s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#455a64' : theme.palette.grey[100],
    transform: 'translateX(5px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 4px 15px rgba(33, 150, 243, 0.3)'
      : 'none',
  },
  '&.Mui-selected': {
    backgroundColor: theme.palette.mode === 'dark' ? '#37474f' : 'grey.200',
  },
}));

const StyledFab = styled(Fab)(({ theme }) => ({
  position: 'fixed',
  bottom: theme.spacing(3),
  right: theme.spacing(3),
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(45deg, #0288d1 30%, #4fc3f7 90%)'
    : 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
  color: '#fff',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 15px rgba(33, 150, 243, 0.5)'
    : '0 4px 15px rgba(0, 0, 0, 0.2)',
  '&:hover': {
    transform: 'scale(1.1)',
    background: theme.palette.mode === 'dark'
      ? 'linear-gradient(45deg, #0277bd 30%, #29b6f6 90%)'
      : 'linear-gradient(45deg, #1976d2 30%, #00bcd4 90%)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 6px 20px rgba(33, 150, 243, 0.7)'
      : '0 6px 20px rgba(0, 0, 0, 0.3)',
  },
  transition: 'all 0.3s ease',
}));

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: '16px',
    background: theme.palette.mode === 'dark'
      ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)'
      : '#fff',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 30px rgba(0, 0, 0, 0.7)'
      : '0 8px 30px rgba(0, 0, 0, 0.15)',
    border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
    width: '100%',
    maxWidth: '800px',
  },
}));

const HeaderBar = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(1),
  background: theme.palette.mode === 'dark'
    ? 'rgba(55, 71, 79, 0.8)'
    : 'rgba(245, 245, 245, 0.9)',
  borderRadius: '12px',
  marginBottom: theme.spacing(2),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 2px 10px rgba(0, 0, 0, 0.5)'
    : '0 2px 10px rgba(0, 0, 0, 0.05)',
}));

const Mail = () => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [mails, setMails] = useState([
    { id: 1, subject: 'Welcome to the new Outlook', body: 'This is a welcome email...', from: '<EMAIL>', date: '2023-10-01', avatar: 'https://i.pravatar.cc/40?img=1' },
    { id: 2, subject: 'Your account has been updated', body: 'We have made some changes to your account...', from: '<EMAIL>', date: '2023-10-02', avatar: 'https://i.pravatar.cc/40?img=2' },
    { id: 3, subject: 'Security Alert', body: 'We detected a login from a new device...', from: '<EMAIL>', date: '2023-10-03', avatar: 'https://i.pravatar.cc/40?img=3' },
  ]);

  const [selectedMail, setSelectedMail] = useState(null);
  const [isComposeOpen, setIsComposeOpen] = useState(false);
  const [isReplyOpen, setIsReplyOpen] = useState(false);
  const [newMail, setNewMail] = useState({ to: '', subject: '', body: '', attachment: null });
  const [expandedMailId, setExpandedMailId] = useState(null);

  const handleMailClick = (mail) => {
    setSelectedMail(mail);
    setExpandedMailId(null); // Collapse any expanded previews
  };

  const handleExpandClick = (id) => {
    setExpandedMailId(expandedMailId === id ? null : id);
    setSelectedMail(null); // Deselect full preview when expanding
  };

  const handleComposeOpen = () => setIsComposeOpen(true);
  const handleComposeClose = () => {
    setIsComposeOpen(false);
    setNewMail({ to: '', subject: '', body: '', attachment: null });
  };

  const handleReplyOpen = () => setIsReplyOpen(true);
  const handleReplyClose = () => {
    setIsReplyOpen(false);
    setNewMail({ to: '', subject: '', body: '', attachment: null });
  };

  const handleSendMail = () => {
    if (newMail.to && newMail.subject && newMail.body) {
      const mail = {
        id: mails.length + 1,
        subject: newMail.subject,
        body: newMail.body,
        from: '<EMAIL>',
        date: new Date().toISOString().split('T')[0],
        attachment: newMail.attachment,
        avatar: 'https://i.pravatar.cc/40?img=4',
      };
      setMails([...mails, mail]);
      handleComposeClose();
      handleReplyClose();
    }
  };

  const handleAttachmentChange = (event) => {
    const file = event.target.files[0];
    if (file) setNewMail({ ...newMail, attachment: file });
  };

  const handleDeleteMail = (id) => {
    setMails(mails.filter((mail) => mail.id !== id));
    if (selectedMail?.id === id) setSelectedMail(null);
  };

  return (
    <MailContainer>
      <Sidebar />

      <Box sx={{ 
        flexGrow: 1, 
        marginLeft: { xs: 0, md: '250px' },
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        zIndex: 1,
      }}>
        <Navbar />

        <Box sx={{ 
          display: 'flex', 
          flexDirection: { xs: 'column', md: 'row' },
          flexGrow: 1,
          p: { xs: 1, md: 0 }
        }}>
          <MailListContainer elevation={3}>
            <HeaderBar>
              <Typography variant="h6" sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary', fontWeight: 600 }}>
                Inbox ({mails.length})
              </Typography>
              <IconButton sx={{ color: isDarkMode ? '#90caf9' : 'primary.main' }}>
                <AddIcon />
              </IconButton>
            </HeaderBar>
            <List sx={{ p: 1 }}>
              {mails.map((mail) => (
                <React.Fragment key={mail.id}>
                  <StyledListItem
                    selected={selectedMail?.id === mail.id}
                    onClick={() => handleMailClick(mail)}
                  >
                    <ListItemAvatar>
                      <Avatar src={mail.avatar} sx={{ border: isDarkMode ? '1px solid #455a64' : 'none' }} />
                    </ListItemAvatar>
                    <ListItemText 
                      primary={
                        <Typography variant="subtitle1" noWrap sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary', fontWeight: 600 }}>
                          {mail.subject}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" color={isDarkMode ? '#b0bec5' : 'text.secondary'} noWrap>
                          {mail.from}
                        </Typography>
                      }
                    />
                    <IconButton
                      edge="end"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpandClick(mail.id);
                      }}
                      sx={{ color: isDarkMode ? '#90caf9' : 'text.secondary' }}
                    >
                      {expandedMailId === mail.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </StyledListItem>
                  <Collapse in={expandedMailId === mail.id} timeout="auto" unmountOnExit>
                    <Box sx={{ p: 2, bgcolor: isDarkMode ? '#2e3b3e' : 'grey.50' }}>
                      <Typography variant="body2" sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary', mb: 1 }}>
                        {mail.body.substring(0, 100)}...
                      </Typography>
                      <Typography variant="caption" sx={{ color: isDarkMode ? '#90caf9' : 'text.secondary' }}>
                        {mail.date}
                      </Typography>
                    </Box>
                  </Collapse>
                  {!isMobile && <Divider sx={{ bgcolor: isDarkMode ? '#455a64' : '#e0e0e0' }} />}
                </React.Fragment>
              ))}
            </List>
          </MailListContainer>

          <MailPreviewContainer elevation={3}>
            {selectedMail ? (
              <Box sx={{ animation: 'fadeIn 0.3s ease-in' }}>
                <HeaderBar>
                  <Typography variant="h5" sx={{ fontWeight: 600, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                    {selectedMail.subject}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Reply">
                      <IconButton
                        onClick={handleReplyOpen}
                        sx={{ color: isDarkMode ? '#90caf9' : 'primary.main' }}
                      >
                        <ReplyIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Forward">
                      <IconButton
                        onClick={() => setNewMail({ to: '', subject: `Fwd: ${selectedMail.subject}`, body: selectedMail.body })}
                        sx={{ color: isDarkMode ? '#90caf9' : 'primary.main' }}
                      >
                        <ForwardIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton
                        onClick={() => handleDeleteMail(selectedMail.id)}
                        sx={{ color: isDarkMode ? '#ff5252' : 'error.main' }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </HeaderBar>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar src={selectedMail.avatar} sx={{ mr: 2, border: isDarkMode ? '1px solid #455a64' : 'none' }} />
                  <Box>
                    <Typography variant="subtitle1" sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary', fontWeight: 500 }}>
                      {selectedMail.from}
                    </Typography>
                    <Typography variant="caption" sx={{ color: isDarkMode ? '#b0bec5' : 'text.secondary' }}>
                      {selectedMail.date}
                    </Typography>
                  </Box>
                </Box>
                <Divider sx={{ my: 2, bgcolor: isDarkMode ? '#455a64' : '#e0e0e0' }} />
                <Typography sx={{ lineHeight: 1.8, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                  {selectedMail.body}
                </Typography>
                {selectedMail.attachment && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" sx={{ mb: 1, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                      Attachment:
                    </Typography>
                    <Button
                      variant="contained"
                      sx={{
                        background: isDarkMode 
                          ? 'linear-gradient(45deg, #d81b60 30%, #f06292 90%)' 
                          : 'linear-gradient(45deg, #f06292 30%, #f48fb1 90%)',
                        color: '#fff',
                        borderRadius: '20px',
                        boxShadow: isDarkMode ? '0 4px 15px rgba(33, 150, 243, 0.3)' : 'none',
                        '&:hover': {
                          background: isDarkMode 
                            ? 'linear-gradient(45deg, #c2185b 30%, #ec407a 90%)' 
                            : 'linear-gradient(45deg, #ec407a 30%, #f06292 90%)',
                          transform: 'translateY(-3px)',
                        },
                      }}
                      startIcon={<AttachFileIcon />}
                      onClick={() => alert(`Downloading ${selectedMail.attachment.name}`)}
                    >
                      {selectedMail.attachment.name}
                    </Button>
                  </Box>
                )}
              </Box>
            ) : (
              <Typography 
                variant="h6" 
                sx={{ 
                  textAlign: 'center', 
                  mt: 4,
                  color: isDarkMode ? '#b0bec5' : 'text.secondary'
                }}
              >
                Select a mail to read
              </Typography>
            )}
          </MailPreviewContainer>
        </Box>
      </Box>

      <StyledFab onClick={handleComposeOpen}>
        <AddIcon />
      </StyledFab>

      <StyledDialog 
        open={isComposeOpen || isReplyOpen} 
        onClose={isReplyOpen ? handleReplyClose : handleComposeClose} 
        fullWidth 
        maxWidth="md"
      >
        <DialogTitle 
          sx={{ 
            bgcolor: isDarkMode ? '#37474f' : 'primary.main', 
            color: '#fff',
            fontWeight: 700,
            textShadow: isDarkMode ? '0 2px 4px rgba(0,0,0,0.5)' : 'none',
            borderBottom: isDarkMode ? '1px solid #455a64' : 'none',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {isReplyOpen ? 'Reply' : 'Compose New Mail'}
          <IconButton onClick={isReplyOpen ? handleReplyClose : handleComposeClose} sx={{ color: '#fff' }}>
            <DeleteIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <TextField
            fullWidth
            label="To"
            value={isReplyOpen ? selectedMail?.from : newMail.to}
            onChange={(e) => setNewMail({ ...newMail, to: e.target.value })}
            sx={{ 
              my: 2,
              '& .MuiOutlinedInput-root': { 
                borderRadius: '8px', 
                backgroundColor: isDarkMode ? '#2e3b3e' : '#fff',
                color: isDarkMode ? '#e0e0e0' : 'text.primary',
                transition: 'all 0.3s ease',
                '&:hover': { backgroundColor: isDarkMode ? '#37474f' : '#f5f5f5' },
              },
              '& .MuiInputLabel-root': { color: isDarkMode ? '#b0bec5' : 'text.secondary' },
            }}
            variant="outlined"
          />
          <TextField
            fullWidth
            label="Subject"
            value={isReplyOpen ? `Re: ${selectedMail?.subject}` : newMail.subject}
            onChange={(e) => setNewMail({ ...newMail, subject: e.target.value })}
            sx={{ 
              my: 2,
              '& .MuiOutlinedInput-root': { 
                borderRadius: '8px', 
                backgroundColor: isDarkMode ? '#2e3b3e' : '#fff',
                color: isDarkMode ? '#e0e0e0' : 'text.primary',
                transition: 'all 0.3s ease',
                '&:hover': { backgroundColor: isDarkMode ? '#37474f' : '#f5f5f5' },
              },
              '& .MuiInputLabel-root': { color: isDarkMode ? '#b0bec5' : 'text.secondary' },
            }}
            variant="outlined"
          />
          <TextField
            fullWidth
            multiline
            rows={6}
            label="Body"
            value={newMail.body}
            onChange={(e) => setNewMail({ ...newMail, body: e.target.value })}
            sx={{ 
              my: 2,
              '& .MuiOutlinedInput-root': { 
                borderRadius: '8px', 
                backgroundColor: isDarkMode ? '#2e3b3e' : '#fff',
                color: isDarkMode ? '#e0e0e0' : 'text.primary',
                transition: 'all 0.3s ease',
                '&:hover': { backgroundColor: isDarkMode ? '#37474f' : '#f5f5f5' },
              },
              '& .MuiInputLabel-root': { color: isDarkMode ? '#b0bec5' : 'text.secondary' },
            }}
            variant="outlined"
          />
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
            <input
              type="file"
              id="attachment"
              style={{ display: 'none' }}
              onChange={handleAttachmentChange}
            />
            <label htmlFor="attachment">
              <Button 
                variant="outlined" 
                component="span" 
                startIcon={<AttachFileIcon />}
                sx={{ 
                  borderRadius: '20px',
                  borderColor: isDarkMode ? '#90caf9' : 'primary.main',
                  color: isDarkMode ? '#90caf9' : 'primary.main',
                  '&:hover': {
                    borderColor: isDarkMode ? '#42a5f5' : 'primary.dark',
                    backgroundColor: isDarkMode ? '#455a6422' : 'rgba(33, 150, 243, 0.1)',
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                Attach File
              </Button>
            </label>
            {newMail.attachment && (
              <Typography variant="body2" sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                Attached: {newMail.attachment.name}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
          <Button 
            onClick={isReplyOpen ? handleReplyClose : handleComposeClose}
            sx={{ 
              borderRadius: '20px',
              color: isDarkMode ? '#e0e0e0' : 'text.primary',
              '&:hover': {
                backgroundColor: isDarkMode ? '#455a64' : 'action.hover',
                transform: 'scale(1.05)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Discard
          </Button>
          <Button 
            onClick={handleSendMail} 
            variant="contained"
            sx={{ 
              borderRadius: '20px',
              background: isDarkMode 
                ? 'linear-gradient(45deg, #0288d1 30%, #4fc3f7 90%)' 
                : 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
              color: '#fff',
              boxShadow: isDarkMode ? '0 4px 15px rgba(33, 150, 243, 0.3)' : 'none',
              '&:hover': {
                background: isDarkMode 
                  ? 'linear-gradient(45deg, #0277bd 30%, #29b6f6 90%)' 
                  : 'linear-gradient(45deg, #1976d2 30%, #00bcd4 90%)',
                transform: 'translateY(-3px)',
                boxShadow: isDarkMode ? '0 6px 20px rgba(33, 150, 243, 0.5)' : 'none',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Send
          </Button>
        </DialogActions>
      </StyledDialog>

      <style>
        {`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }
          @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
          }
        `}
      </style>
    </MailContainer>
  );
};

export default Mail;