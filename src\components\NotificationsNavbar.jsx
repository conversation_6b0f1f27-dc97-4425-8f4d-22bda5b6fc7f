import React from 'react';
import {
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  IconButton,
  Slide,
  Avatar,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Close as CloseIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';

// Styled Components
const NotificationsMenu = styled(Menu)(({ theme, isDarkMode }) => ({
  '& .MuiPaper-root': {
    width: '360px', // Slightly wider for avatars and content
    maxHeight: '450px',
    borderRadius: '16px',
    background: isDarkMode
      ? 'linear-gradient(145deg, #2c3e50, #34495e)'
      : 'linear-gradient(145deg, #ffffff, #f5f5f5)',
    boxShadow: '0 10px 40px rgba(0, 0, 0, 0.25)',
    border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)',
    overflowY: 'auto',
  },
}));

const NotificationItem = styled(MenuItem)(({ theme, isDarkMode }) => ({
  padding: '12px 16px',
  transition: 'all 0.3s ease',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  '&:hover': {
    backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(63, 81, 181, 0.1)', // Blue-tinted hover
    transform: 'scale(1.02)',
  },
}));

const NotificationsNavbar = ({ anchorEl, open, onClose, isDarkMode }) => {
  const notifications = [
    {
      id: 1,
      user: 'Alice Johnson',
      picture: 'https://via.placeholder.com/40',
      message: 'New case assigned: Smith v. Jones',
      time: '5 mins ago',
      read: false,
    },
    {
      id: 2,
      user: 'Bob Smith',
      picture: 'https://via.placeholder.com/40',
      message: 'Invoice #1234 paid',
      time: '1 hr ago',
      read: true,
    },
    {
      id: 3,
      user: 'Charlie Brown',
      picture: 'https://via.placeholder.com/40',
      message: 'Court date scheduled for Doe v. Roe',
      time: '2 hrs ago',
      read: false,
    },
  ];

  const handleMarkAsRead = (id) => {
    // Logic to mark notification as read (e.g., update state or API call)
    console.log(`Marked notification ${id} as read`);
  };

  return (
    <NotificationsMenu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      isDarkMode={isDarkMode}
      TransitionComponent={Slide}
      transitionDuration={300}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          background: isDarkMode ? 'rgba(30, 42, 56, 0.9)' : 'rgba(245, 245, 245, 0.9)',
          borderBottom: `1px solid ${isDarkMode ? '#3d4e60' : '#e0e0e0'}`,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{ fontWeight: 600, color: isDarkMode ? '#fff' : '#000' }}
        >
          Notifications
        </Typography>
        <IconButton size="small" onClick={onClose}>
          <CloseIcon sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }} />
        </IconButton>
      </Box>

      {/* Notification List */}
      {notifications.length > 0 ? (
        notifications.map((notification) => (
          <NotificationItem key={notification.id} isDarkMode={isDarkMode}>
            <Avatar
              src={notification.picture}
              alt={notification.user}
              sx={{
                width: 40,
                height: 40,
                border: `2px solid ${notification.read ? (isDarkMode ? '#66bb6a' : '#4caf50') : (isDarkMode ? '#90caf9' : '#3f51b5')}`,
              }}
            />
            <Box sx={{ flexGrow: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: isDarkMode ? '#fff' : '#000',
                  fontWeight: notification.read ? 400 : 600,
                }}
              >
                <strong>{notification.user}</strong>: {notification.message}
              </Typography>
              <Typography
                variant="caption"
                sx={{ color: isDarkMode ? '#b0bec5' : '#757575' }}
              >
                {notification.time}
              </Typography>
            </Box>
            {!notification.read && (
              <IconButton
                size="small"
                onClick={() => handleMarkAsRead(notification.id)}
                sx={{ color: isDarkMode ? '#90caf9' : '#3f51b5' }}
              >
                <CheckIcon />
              </IconButton>
            )}
          </NotificationItem>
        ))
      ) : (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography
            variant="body2"
            sx={{ color: isDarkMode ? '#b0bec5' : '#757575' }}
          >
            No notifications
          </Typography>
        </Box>
      )}

      {/* Footer */}
      <Divider sx={{ my: 1, backgroundColor: isDarkMode ? '#3d4e60' : '#e0e0e0' }} />
      <Box
        sx={{
          p: 1,
          textAlign: 'center',
          background: isDarkMode ? 'rgba(30, 42, 56, 0.9)' : 'rgba(245, 245, 245, 0.9)',
        }}
      >
        <Typography
          variant="caption"
          sx={{
            color: isDarkMode ? '#90caf9' : '#3f51b5',
            cursor: 'pointer',
            '&:hover': { textDecoration: 'underline' },
          }}
          onClick={() => console.log('View all notifications')}
        >
          View All
        </Typography>
      </Box>
    </NotificationsMenu>
  );
};

export default NotificationsNavbar;