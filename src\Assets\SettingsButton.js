import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Divider, 
  Paper, 
  Button, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  IconButton, 
  Switch, 
  ListItemIcon,
  ListItemText,
  Slide,
  Fade,
  styled,
  Slider,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Notifications as NotificationsIcon,
  Close as CloseIcon,
  Check as CheckIcon,
  Wallpaper as WallpaperIcon,
} from '@mui/icons-material';
import { useTheme } from '../Assets/ThemeContext';
import { useTranslation } from 'react-i18next';
import defaultBg from './Pictures/sidebar-bg-default.jpg';
import abstractBg1 from './Pictures/abstract-bg-1.jpg';
import abstractBg2 from './Pictures/abstract-bg-2.jpg';
import { useSettings } from '../context/SettingsContext';

// Styled components
const FloatingButton = styled(IconButton)(({ theme }) => ({
  position: 'fixed',
  right: '20px',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 1500,
  width: '60px',
  height: '60px',
  borderRadius: '50%',
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, #0288d1 0%, #4fc3f7 100%)'
    : 'linear-gradient(135deg, #2196f3 0%, #42a5f5 100%)',
  boxShadow: '0 6px 20px rgba(33, 150, 243, 0.4)',
  color: '#fff',
  animation: 'pulse 2s infinite',
  '&:hover': {
    transform: 'translateY(-50%) scale(1.1)',
    boxShadow: '0 8px 25px rgba(33, 150, 243, 0.6)',
    background: theme.palette.mode === 'dark'
      ? 'linear-gradient(135deg, #0277bd 0%, #29b6f6 100%)'
      : 'linear-gradient(135deg, #1976d2 0%, #0288d1 100%)',
  },
  transition: 'all 0.3s ease',
}));

const SettingsPanel = styled(Paper)(({ theme }) => ({
  position: 'fixed',
  right: '15px',
  top: '10%',
  transform: 'translateY(-50%)',
  width: '400px',
  maxWidth: '90vw',
  maxHeight: '85vh',
  overflowY: 'auto',
  padding: theme.spacing(3),
  background: theme.palette.mode === 'dark'
    ? 'rgba(38, 50, 56, 0.9)' // Glassmorphism effect
    : 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  borderRadius: '20px',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : '1px solid rgba(0, 0, 0, 0.1)',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 10px 40px rgba(0, 0, 0, 0.7)'
    : '0 10px 40px rgba(0, 0, 0, 0.15)',
  zIndex: 1500,
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: theme.palette.mode === 'dark' ? '#90caf9' : '#2196f3',
    borderRadius: '3px',
  },
}));

const TabButton = styled(Button)(({ theme, active }) => ({
  flex: 1,
  borderRadius: '12px',
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1),
  background: active
    ? theme.palette.mode === 'dark'
      ? 'linear-gradient(145deg, #37474f 0%, #455a64 100%)'
      : 'linear-gradient(145deg, #e3f2fd 0%, #bbdefb 100%)'
    : 'transparent',
  color: active
    ? theme.palette.mode === 'dark' ? '#e0e0e0' : '#1976d2'
    : theme.palette.text.secondary,
  boxShadow: active ? '0 2px 10px rgba(0, 0, 0, 0.2)' : 'none',
  '&:hover': {
    background: active
      ? theme.palette.mode === 'dark'
        ? 'linear-gradient(145deg, #455a64 0%, #546e7a 100%)'
        : 'linear-gradient(145deg, #bbdefb 0%, #90caf9 100%)'
      : theme.palette.action.hover,
    transform: 'scale(1.02)',
  },
  transition: 'all 0.3s ease',
}));

const ColorOption = styled(Box)(({ theme, color, selected }) => ({
  width: '28px',
  height: '28px',
  borderRadius: '50%',
  backgroundColor: color,
  border: selected
    ? `3px solid ${theme.palette.primary.main}`
    : `2px solid ${theme.palette.divider}`,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.15)',
    boxShadow: `0 0 8px ${color}`,
  },
}));

const BackgroundOption = styled(Box)(({ theme, selected }) => ({
  position: 'relative',
  borderRadius: '12px',
  overflow: 'hidden',
  height: '90px',
  cursor: 'pointer',
  border: selected
    ? `3px solid ${theme.palette.primary.main}`
    : '3px solid transparent',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 4px 15px rgba(33, 150, 243, 0.3)'
      : '0 4px 15px rgba(0, 0, 0, 0.1)',
  },
}));

const SettingsButton = () => {
  const { isDarkMode, toggleTheme } = useTheme();
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  const toggleWindow = () => setIsOpen(!isOpen);
  const handleLanguageChange = (lang) => i18n.changeLanguage(lang);

  const sidebarColors = [
    { value: '#34495e', name: 'Default (Dark)' },
    { value: '#2c3e50', name: 'Dark Blue' },
    { value: '#4a148c', name: 'Deep Purple' },
    { value: '#00695c', name: 'Teal' },
  ];

  const navbarColors = [
    { value: '#3f51b5', name: 'Default (Blue)' },
    { value: '#d32f2f', name: 'Red' },
    { value: '#388e3c', name: 'Green' },
    { value: '#f57c00', name: 'Orange' },
  ];

  const backgroundOptions = [
    { value: null, name: 'None' },
    { value: defaultBg, name: 'Default' },
    { value: abstractBg1, name: 'Abstract 1' },
    { value: abstractBg2, name: 'Abstract 2' },
  ];

  const { sidebarColor, setSidebarColor, navbarColor, setNavbarColor, sidebarBg, setSidebarBg, bgOpacity, setBgOpacity } =
    useSettings();

  useEffect(() => {
    document.documentElement.style.setProperty('--sidebar-color', sidebarColor);
    document.documentElement.style.setProperty('--navbar-color', navbarColor);
  }, [sidebarColor, navbarColor]);

  return (
    <>
      <Fade in={!isOpen}>
        <FloatingButton onClick={toggleWindow}>
          <SettingsIcon sx={{ fontSize: '28px' }} />
        </FloatingButton>
      </Fade>

      <Slide direction="left" in={isOpen} mountOnEnter unmountOnExit>
        <SettingsPanel elevation={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 700, color: isDarkMode ? '#e0e0e0' : '#1976d2', letterSpacing: '0.5px' }}>
              {t('settings')}
            </Typography>
            <IconButton
              onClick={toggleWindow}
              sx={{
                color: isDarkMode ? '#90caf9' : 'primary.main',
                '&:hover': { transform: 'rotate(90deg)', color: isDarkMode ? '#42a5f5' : 'primary.dark' },
                transition: 'all 0.3s ease',
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Settings Tabs */}
          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
            <TabButton active={activeTab === 'general'} onClick={() => setActiveTab('general')}>
              General
            </TabButton>
            <TabButton active={activeTab === 'appearance'} onClick={() => setActiveTab('appearance')}>
              Appearance
            </TabButton>
            <TabButton active={activeTab === 'notifications'} onClick={() => setActiveTab('notifications')}>
              Notifications
            </TabButton>
          </Box>

          {/* General Settings */}
          {activeTab === 'general' && (
            <Box sx={{ animation: 'fadeIn 0.3s ease' }}>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel id="language-select-label" sx={{ color: isDarkMode ? '#b0bec5' : 'text.secondary', '&.Mui-focused': { color: isDarkMode ? '#90caf9' : 'primary.main' } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LanguageIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    {t('language')}
                  </Box>
                </InputLabel>
                <Select
                  labelId="language-select-label"
                  value={i18n.language}
                  onChange={(e) => handleLanguageChange(e.target.value)}
                  label={<Box sx={{ display: 'flex', alignItems: 'center' }}><LanguageIcon sx={{ mr: 1, fontSize: '1.2rem' }} />{t('language')}</Box>}
                  sx={{
                    borderRadius: '12px',
                    backgroundColor: isDarkMode ? '#2e3b3e' : '#fff',
                    '& .MuiSelect-select': { display: 'flex', alignItems: 'center', color: isDarkMode ? '#e0e0e0' : 'text.primary' },
                    '&:hover': { backgroundColor: isDarkMode ? '#37474f' : '#f5f5f5' },
                  }}
                >
                  {['en', 'es', 'fr', 'ar'].map((lang) => (
                    <MenuItem key={lang} value={lang} sx={{ animation: 'slideIn 0.2s ease' }}>
                      <ListItemIcon>
                        <CheckIcon sx={{ opacity: i18n.language === lang ? 1 : 0, color: 'primary.main' }} />
                      </ListItemIcon>
                      <ListItemText>{lang === 'en' ? 'English' : lang === 'es' ? 'Spanish' : lang === 'fr' ? 'French' : 'Arabic'}</ListItemText>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Divider sx={{ my: 2, bgcolor: isDarkMode ? '#455a64' : '#e0e0e0' }} />

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 2,
                  borderRadius: '12px',
                  bgcolor: isDarkMode ? '#2e3b3e' : 'background.default',
                  '&:hover': { bgcolor: isDarkMode ? '#37474f' : '#f5f5f5' },
                  transition: 'all 0.3s ease',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {isDarkMode ? <DarkModeIcon sx={{ mr: 1, color: '#90caf9' }} /> : <LightModeIcon sx={{ mr: 1, color: '#ffb300' }} />}
                  <Typography sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>{t('dark_mode')}</Typography>
                </Box>
                <Switch
                  checked={isDarkMode}
                  onChange={toggleTheme}
                  sx={{
                    '& .MuiSwitch-track': { backgroundColor: isDarkMode ? '#0288d1' : '#e0e0e0' },
                    '& .MuiSwitch-thumb': { backgroundColor: isDarkMode ? '#4fc3f7' : '#fff' },
                  }}
                />
              </Box>
            </Box>
          )}

          {/* Appearance Settings */}
          {activeTab === 'appearance' && (
            <Box sx={{ animation: 'fadeIn 0.3s ease' }}>
              {/* Sidebar Color */}
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 2, fontWeight: 600, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                <PaletteIcon sx={{ mr: 1, color: isDarkMode ? '#90caf9' : 'primary.main' }} /> Sidebar Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
                {sidebarColors.map((color) => (
                  <Box key={color.value} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }} onClick={() => setSidebarColor(color.value)}>
                    <ColorOption color={color.value} selected={sidebarColor === color.value} />
                    <Typography variant="caption" sx={{ mt: 0.5, color: isDarkMode ? '#b0bec5' : 'text.secondary' }}>{color.name}</Typography>
                  </Box>
                ))}
              </Box>

              {/* Sidebar Background */}
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 2, fontWeight: 600, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                <WallpaperIcon sx={{ mr: 1, color: isDarkMode ? '#90caf9' : 'primary.main' }} /> Sidebar Background
              </Typography>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {backgroundOptions.filter((bg) => bg.value).map((bg) => (
                    <BackgroundOption key={bg.name} selected={sidebarBg === bg.value} onClick={() => setSidebarBg(bg.value)}>
                      <Box
                        component="img"
                        src={bg.value}
                        alt={bg.name}
                        sx={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          filter: isDarkMode ? 'brightness(0.8)' : 'brightness(1)',
                        }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          color: '#fff',
                          p: 1,
                          textAlign: 'center',
                          fontSize: '0.8rem',
                          fontWeight: 600,
                        }}
                      >
                        {bg.name}
                      </Box>
                      {sidebarBg === bg.value && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            bgcolor: 'primary.main',
                            borderRadius: '50%',
                            width: '24px',
                            height: '24px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#fff',
                          }}
                        >
                          <CheckIcon fontSize="small" />
                        </Box>
                      )}
                    </BackgroundOption>
                  ))}
                </Box>

                {sidebarBg && (
                  <>
                    <Typography variant="body2" sx={{ mt: 2, mb: 1, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                      Background Opacity: {Math.round(bgOpacity * 100)}%
                    </Typography>
                    <Slider
                      value={bgOpacity}
                      onChange={(e, newValue) => setBgOpacity(newValue)}
                      min={0.1}
                      max={0.7}
                      step={0.05}
                      sx={{
                        color: isDarkMode ? '#90caf9' : 'primary.main',
                        '& .MuiSlider-thumb': { width: '16px', height: '16px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)' },
                        '&:hover': { '& .MuiSlider-thumb': { transform: 'scale(1.2)' } },
                      }}
                    />
                  </>
                )}
              </Box>

              {/* Navbar Color */}
              <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 2, fontWeight: 600, color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>
                <PaletteIcon sx={{ mr: 1, color: isDarkMode ? '#90caf9' : 'primary.main' }} /> Navbar Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
                {navbarColors.map((color) => (
                  <Box key={color.value} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }} onClick={() => setNavbarColor(color.value)}>
                    <ColorOption color={color.value} selected={navbarColor === color.value} />
                    <Typography variant="caption" sx={{ mt: 0.5, color: isDarkMode ? '#b0bec5' : 'text.secondary' }}>{color.name}</Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          )}

          {/* Notification Settings */}
          {activeTab === 'notifications' && (
            <Box sx={{ animation: 'fadeIn 0.3s ease' }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 2,
                  borderRadius: '12px',
                  bgcolor: isDarkMode ? '#2e3b3e' : 'background.default',
                  mb: 2,
                  '&:hover': { bgcolor: isDarkMode ? '#37474f' : '#f5f5f5' },
                  transition: 'all 0.3s ease',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <NotificationsIcon sx={{ mr: 1, color: isDarkMode ? '#90caf9' : 'primary.main' }} />
                  <Typography sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>Enable Notifications</Typography>
                </Box>
                <Switch
                  checked={notificationsEnabled}
                  onChange={() => setNotificationsEnabled(!notificationsEnabled)}
                  sx={{
                    '& .MuiSwitch-track': { backgroundColor: isDarkMode ? '#0288d1' : '#e0e0e0' },
                    '& .MuiSwitch-thumb': { backgroundColor: isDarkMode ? '#4fc3f7' : '#fff' },
                  }}
                />
              </Box>

              <Typography variant="body2" sx={{ color: isDarkMode ? '#b0bec5' : 'text.secondary', mb: 3 }}>
                Customize which notifications you want to receive
              </Typography>

              {['System Alerts', 'Email Notifications', 'Push Notifications'].map((type, index) => (
                <Box
                  key={index}
                  sx={{
                    p: 2,
                    borderRadius: '12px',
                    bgcolor: isDarkMode ? '#2e3b3e' : 'background.default',
                    mb: 2,
                    '&:hover': { bgcolor: isDarkMode ? '#37474f' : '#f5f5f5' },
                    transition: 'all 0.3s ease',
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography sx={{ color: isDarkMode ? '#e0e0e0' : 'text.primary' }}>{type}</Typography>
                    <Switch
                      checked={notificationsEnabled}
                      size="small"
                      sx={{
                        '& .MuiSwitch-track': { backgroundColor: isDarkMode ? '#0288d1' : '#e0e0e0' },
                        '& .MuiSwitch-thumb': { backgroundColor: isDarkMode ? '#4fc3f7' : '#fff' },
                      }}
                    />
                  </Box>
                  <Typography variant="body2" sx={{ color: isDarkMode ? '#b0bec5' : 'text.secondary' }}>
                    {type === 'System Alerts' ? 'Important system notifications and updates' : type === 'Email Notifications' ? 'Receive notifications via email' : 'Get real-time updates on your device'}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}

          <Divider sx={{ my: 3, bgcolor: isDarkMode ? '#455a64' : '#e0e0e0' }} />

          <Button
            onClick={toggleWindow}
            fullWidth
            variant="contained"
            sx={{
              borderRadius: '12px',
              py: 1.5,
              fontWeight: 600,
              textTransform: 'none',
              background: isDarkMode
                ? 'linear-gradient(135deg, #0288d1 0%, #4fc3f7 100%)'
                : 'linear-gradient(135deg, #2196f3 0%, #42a5f5 100%)',
              boxShadow: '0 4px 15px rgba(33, 150, 243, 0.3)',
              '&:hover': {
                background: isDarkMode
                  ? 'linear-gradient(135deg, #0277bd 0%, #29b6f6 100%)'
                  : 'linear-gradient(135deg, #1976d2 0%, #0288d1 100%)',
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 20px rgba(33, 150, 243, 0.5)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Save Settings
          </Button>
        </SettingsPanel>
      </Slide>

      <style>
        {`
          @keyframes pulse {
            0% { box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4); }
            50% { box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6); }
            100% { box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4); }
          }
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }
          @keyframes slideIn {
            from { opacity: 0; transform: translateX(-10px); }
            to { opacity: 1; transform: translateX(0); }
          }
        `}
      </style>
    </>
  );
};

export default SettingsButton;