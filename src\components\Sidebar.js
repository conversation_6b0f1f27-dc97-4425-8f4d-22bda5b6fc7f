import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Box, 
  Button, 
  Typography, 
  Divider,
  styled,
  useTheme as muiUseTheme
} from '@mui/material';
import {
  Home as HomeIcon,
  People as PeopleIcon,
  AttachMoney as AttachMoneyIcon,
  Computer as ComputerIcon,
  Folder as FolderIcon,
  Mail as MailIcon,
  CalendarToday as CalendarTodayIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import LogoutButton from './UI/LogoutButton';
import { useTheme } from '../Assets/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useSettings } from '../context/SettingsContext';

// Enhanced Sidebar Container
const SidebarContainer = styled(Box, {
  shouldForwardProp: (prop) => !['isopen', 'sidebarbg', 'bgopacity', 'isdarkmode'].includes(prop),
})(({ theme, isopen, sidebarbg, bgopacity, isdarkmode }) => {
  const isOpen = isopen === 'true';
  const isDark = isdarkmode === 'true';
  
  const baseStyles = {
    width: isOpen ? '250px' : '0',
    height: '100vh',
    padding: isOpen ? theme.spacing(2) : 0,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRight: `1px solid ${isDark ? '#455a64' : '#e0e0e0'}`,
    boxShadow: isDark ? '0 4px 20px rgba(0,0,0,0.5)' : '0 4px 20px rgba(0,0,0,0.1)',
    position: 'fixed',
    top: 0,
    left: 0,
    zIndex: theme.zIndex.drawer,
    overflow: 'hidden',
    transition: theme.transitions.create(['width', 'padding'], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.standard,
    }),
  };

  if (sidebarbg) {
    return {
      ...baseStyles,
      background: `
        linear-gradient(
          rgba(0, 0, 0, ${isDark ? 0.7 : 0.5}),
          rgba(0, 0, 0, ${isDark ? 0.7 : 0.5})
        ),
        url(${sidebarbg})
      `,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: isDark 
          ? `rgba(44, 62, 80, ${bgopacity})` 
          : `rgba(245, 245, 245, ${bgopacity})`,
        zIndex: -1,
      },
    };
  }

  return {
    ...baseStyles,
    background: isDark
      ? 'linear-gradient(135deg, #2c3e50 0%, #1e2a38 100%)'
      : 'linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%)',
  };
});

// Styled Menu Button
const MenuButton = styled(Button)(({ theme, active, isdarkmode }) => ({
  borderRadius: '12px',
  textTransform: 'none',
  justifyContent: 'flex-start',
  padding: theme.spacing(1.5, 2),
  fontSize: '0.9rem',
  fontWeight: 600,
  color: active === 'true' ? '#fff' : isdarkmode === 'true' ? '#e0e0e0' : '#1e3c72',
  backgroundColor: active === 'true'
    ? 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)'
    : isdarkmode === 'true' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
  transition: 'all 0.3s ease-in-out',
  boxShadow: active === 'true' ? '0 4px 15px rgba(33, 150, 243, 0.4)' : 'none',
  '&:hover': {
    backgroundColor: active === 'true'
      ? 'linear-gradient(45deg, #1976d2 30%, #00bcd4 90%)'
      : isdarkmode === 'true' ? 'rgba(255,255,255,0.15)' : 'rgba(33, 150, 243, 0.1)',
    transform: 'translateY(-3px)',
    boxShadow: '0 6px 20px rgba(33, 150, 243, 0.3)',
  },
  '& .MuiButton-startIcon': {
    marginRight: theme.spacing(1.5),
  },
}));

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeButton, setActiveButton] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { sidebarBg, bgOpacity, sidebarColor, navbarColor } = useSettings();

  const menuItems = [
    { text: t('home'), icon: <HomeIcon />, path: '/home' },
    { text: t('hr'), icon: <PeopleIcon />, path: '/hr' },
    { text: t('finance'), icon: <AttachMoneyIcon />, path: '/finance' },
    { text: t('it'), icon: <ComputerIcon />, path: '/it' },
    { text: t('fileManagement'), icon: <FolderIcon />, path: '/file-management' },
    { text: t('sales'), icon: <AttachMoneyIcon />, path: '/sales' },
    { text: t('mail'), icon: <MailIcon />, path: '/mail' },
    { text: t('calendar'), icon: <CalendarTodayIcon />, path: '/calendar' },
    { text: t('tasks'), icon: <AssignmentIcon />, path: '/tasks' },
  ];

  useEffect(() => {
    const activeIndex = menuItems.findIndex((item) => item.path === location.pathname);
    setActiveButton(activeIndex);
  }, [location.pathname]);

  const handleButtonClick = (index) => {
    setActiveButton(index);
    navigate(menuItems[index].path);
  };

  useEffect(() => {
    console.log('Current Sidebar Settings:', { 
      sidebarBg, 
      bgOpacity,
      sidebarColor,
      navbarColor
    });
    document.documentElement.style.setProperty('--sidebar-color', sidebarColor);
    document.documentElement.style.setProperty('--navbar-color', navbarColor);
  }, [sidebarColor, navbarColor, sidebarBg, bgOpacity]);

  return (
    <SidebarContainer 
      isopen={isSidebarOpen.toString()}
      sidebarbg={sidebarBg}
      bgopacity={bgOpacity || 0.3}
      isdarkmode={isDarkMode.toString()}
    >
      {/* Top Section */}
      <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        {/* Logo */}
        <Box
          sx={{
            mb: 3,
            p: 1.5,
            borderRadius: '12px',
            background: isDarkMode 
              ? 'linear-gradient(45deg, #34495e 0%, #455a64 100%)' 
              : 'linear-gradient(45deg, #3f51b5 0%, #2196f3 100%)',
            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
            transition: 'transform 0.3s ease',
            '&:hover': {
              transform: 'scale(1.05)',
            },
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: '#fff',
              fontFamily: 'Orbitron, sans-serif',
              letterSpacing: '2px',
              textShadow: '0 2px 8px rgba(0,0,0,0.3)',
            }}
          >
            INFINITY
          </Typography>
        </Box>

        <Divider sx={{ 
          width: '80%', 
          mb: 3, 
          bgcolor: isDarkMode ? '#455a64' : '#e0e0e0',
          height: '1px',
          borderRadius: '2px',
        }} />

        {/* Menu Buttons */}
        {isSidebarOpen && (
          <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            {menuItems.map((item, index) => (
              <MenuButton
                key={index}
                variant="contained"
                startIcon={item.icon}
                active={(activeButton === index).toString()}
                isdarkmode={isDarkMode.toString()}
                fullWidth
                onClick={() => handleButtonClick(index)}
              >
                {item.text}
              </MenuButton>
            ))}
          </Box>
        )}
      </Box>

      {/* Logout Button */}
      {isSidebarOpen && (
        <Box sx={{ 
          width: '100%', 
          mt: 2, 
          px: 2,
          animation: 'fadeIn 0.5s ease-in',
        }}>
          <LogoutButton />
          <style>
            {`
              @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
              }
            `}
          </style>
        </Box>
      )}
    </SidebarContainer>
  );
};

export default Sidebar;