package com.ppg.ems_server_side_v0.controller;

import com.ppg.ems_server_side_v0.model.api.request.JobDescriptionDTO;
import com.ppg.ems_server_side_v0.model.api.response.JobDescriptionResponse;
import com.ppg.ems_server_side_v0.service.core.JobDescriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/hr/job-descriptions")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"}, allowCredentials = "true")
public class JobDescriptionController {

    private final JobDescriptionService jobDescriptionService;

    @GetMapping
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<List<JobDescriptionResponse>> getAllJobDescriptions() {
        try {
            log.info("Fetching all job descriptions");
            List<JobDescriptionResponse> jobDescriptions = jobDescriptionService.getAllJobDescriptions();
            log.info("Found {} job descriptions", jobDescriptions.size());
            return ResponseEntity.ok(jobDescriptions);
        } catch (Exception e) {
            log.error("Error fetching job descriptions: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<JobDescriptionResponse> getJobDescriptionById(@PathVariable String id) {
        try {
            log.info("Fetching job description with ID: {}", id);
            JobDescriptionResponse jobDescription = jobDescriptionService.getJobDescriptionById(id);
            return ResponseEntity.ok(jobDescription);
        } catch (Exception e) {
            log.error("Error fetching job description with ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<JobDescriptionResponse> createJobDescription(
            @RequestBody JobDescriptionDTO jobDescriptionDTO,
            Authentication authentication) {
        try {
            log.info("Creating new job description: {}", jobDescriptionDTO.title());
            String userId = authentication.getName(); // Get user ID from authentication
            JobDescriptionResponse jobDescription = jobDescriptionService.createJobDescription(jobDescriptionDTO, userId);
            log.info("Created job description with ID: {}", jobDescription.id());
            return ResponseEntity.status(HttpStatus.CREATED).body(jobDescription);
        } catch (Exception e) {
            log.error("Error creating job description: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<JobDescriptionResponse> updateJobDescription(
            @PathVariable String id,
            @RequestBody JobDescriptionDTO jobDescriptionDTO) {
        try {
            log.info("Updating job description with ID: {}", id);
            JobDescriptionResponse jobDescription = jobDescriptionService.updateJobDescription(id, jobDescriptionDTO);
            log.info("Updated job description with ID: {}", id);
            return ResponseEntity.ok(jobDescription);
        } catch (Exception e) {
            log.error("Error updating job description with ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<Void> deleteJobDescription(@PathVariable String id) {
        try {
            log.info("Deleting job description with ID: {}", id);
            jobDescriptionService.deleteJobDescription(id);
            log.info("Deleted job description with ID: {}", id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("Error deleting job description with ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @GetMapping("/department/{department}")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<List<JobDescriptionResponse>> getJobDescriptionsByDepartment(@PathVariable String department) {
        try {
            log.info("Fetching job descriptions for department: {}", department);
            List<JobDescriptionResponse> jobDescriptions = jobDescriptionService.getJobDescriptionsByDepartment(department);
            log.info("Found {} job descriptions for department: {}", jobDescriptions.size(), department);
            return ResponseEntity.ok(jobDescriptions);
        } catch (Exception e) {
            log.error("Error fetching job descriptions for department {}: {}", department, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<List<JobDescriptionResponse>> searchJobDescriptions(@RequestParam String keyword) {
        try {
            log.info("Searching job descriptions with keyword: {}", keyword);
            List<JobDescriptionResponse> jobDescriptions = jobDescriptionService.searchJobDescriptions(keyword);
            log.info("Found {} job descriptions matching keyword: {}", jobDescriptions.size(), keyword);
            return ResponseEntity.ok(jobDescriptions);
        } catch (Exception e) {
            log.error("Error searching job descriptions with keyword {}: {}", keyword, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<List<JobDescriptionResponse>> getJobDescriptionsByUser(@PathVariable String userId) {
        try {
            log.info("Fetching job descriptions created by user: {}", userId);
            List<JobDescriptionResponse> jobDescriptions = jobDescriptionService.getJobDescriptionsByUser(userId);
            log.info("Found {} job descriptions created by user: {}", jobDescriptions.size(), userId);
            return ResponseEntity.ok(jobDescriptions);
        } catch (Exception e) {
            log.error("Error fetching job descriptions for user {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
