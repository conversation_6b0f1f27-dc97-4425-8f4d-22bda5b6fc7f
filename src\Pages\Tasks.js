import React, { useState } from "react";
import {
  Box,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Paper,
  Grid,
  Card,
  CardContent,
  IconButton,
  Chip,
  LinearProgress,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import Sidebar from "../components/Sidebar";
import Navbar from "../components/Navbar";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { Delete, Edit, Search } from "@mui/icons-material";
import { useTheme as useAppTheme } from "../Assets/ThemeContext";

const TasksContainer = styled(Box)(({ theme }) => ({
  background: theme.palette.mode === "dark"
    ? "linear-gradient(135deg, #1a2525 0%, #263238 100%)"
    : "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
  minHeight: "100vh",
  display: "flex",
}));

const KanbanColumn = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  minHeight: "400px",
  background: theme.palette.mode === "dark" ? "#263238" : "#f4f6f8",
  borderRadius: "12px",
  boxShadow: theme.palette.mode === "dark"
    ? "0 4px 20px rgba(33, 150, 243, 0.3)"
    : "0 4px 20px rgba(0, 0, 0, 0.1)",
  border: theme.palette.mode === "dark" ? "1px solid #455a64" : "none",
  transition: "all 0.3s ease",
  "&:hover": {
    transform: "translateY(-5px)",
  },
}));

const TaskCard = styled(Card)(({ theme, priority }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: "12px",
  background: theme.palette.mode === "dark"
    ? "linear-gradient(145deg, #37474f 0%, #263238 100%)"
    : "#fff",
  boxShadow: theme.palette.mode === "dark"
    ? "0 6px 20px rgba(33, 150, 243, 0.5)"
    : "0 2px 10px rgba(0, 0, 0, 0.1)",
  borderLeft: priority === "High" ? "4px solid #d32f2f" : priority === "Medium" ? "4px solid #ff9800" : "4px solid #4caf50",
  transition: "all 0.3s ease",
  "&:hover": {
    transform: "scale(1.02)",
  },
}));

const Tasks = () => {
  const { isDarkMode } = useAppTheme();
  const [tasks, setTasks] = useState([]);
  const [newTask, setNewTask] = useState({
    title: "",
    description: "",
    deadline: "",
    assignedTo: "",
    status: "To Do",
    priority: "Medium",
    progress: 0,
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [editTaskId, setEditTaskId] = useState(null);

  const handleAddTask = () => {
    if (newTask.title && newTask.deadline && newTask.assignedTo) {
      setTasks([...tasks, { id: Date.now(), ...newTask }]);
      setNewTask({ title: "", description: "", deadline: "", assignedTo: "", status: "To Do", priority: "Medium", progress: 0 });
    }
  };

  const handleEditTask = (task) => {
    setEditTaskId(task.id);
    setNewTask({ ...task });
  };

  const handleSaveEdit = () => {
    setTasks(tasks.map((t) => (t.id === editTaskId ? { ...newTask, id: t.id } : t)));
    setEditTaskId(null);
    setNewTask({ title: "", description: "", deadline: "", assignedTo: "", status: "To Do", priority: "Medium", progress: 0 });
  };

  const handleDeleteTask = (id) => {
    setTasks(tasks.filter((task) => task.id !== id));
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destIndex = result.destination.index;
    const sourceDroppableId = result.source.droppableId;
    const destDroppableId = result.destination.droppableId;

    const updatedTasks = Array.from(tasks);
    const [movedTask] = updatedTasks.splice(sourceIndex, 1);
    movedTask.status = destDroppableId;
    updatedTasks.splice(destIndex, 0, movedTask);

    setTasks(updatedTasks);
  };

  const getDueStatus = (deadline) => {
    const today = new Date();
    const dueDate = new Date(deadline);
    const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
    return diffDays < 0 ? "Overdue" : diffDays <= 2 ? "Due Soon" : "On Track";
  };

  const filterTasks = (task) =>
    task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    task.assignedTo.toLowerCase().includes(searchQuery.toLowerCase());

  return (
    <TasksContainer>
      <Sidebar />
      <Box sx={{ flexGrow: 1, marginLeft: "250px", p: 3 }}>
        <Navbar />
        <Typography variant="h4" sx={{ mb: 3, color: isDarkMode ? "#e0e0e0" : "#333" }}>
          Task & Project Management
        </Typography>

        {/* Task Input Section */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: "16px", background: isDarkMode ? "#263238" : "#fff", border: isDarkMode ? "1px solid #455a64" : "none" }}>
          <Typography variant="h6" sx={{ mb: 2, color: isDarkMode ? "#e0e0e0" : "#333" }}>
            {editTaskId ? "Edit Task" : "Add New Task"}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Task Title"
                value={newTask.title}
                onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
                fullWidth
                sx={{ input: { color: isDarkMode ? "#e0e0e0" : "#333" } }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Description"
                value={newTask.description}
                onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                fullWidth
                sx={{ input: { color: isDarkMode ? "#e0e0e0" : "#333" } }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Deadline"
                type="date"
                value={newTask.deadline}
                onChange={(e) => setNewTask({ ...newTask, deadline: e.target.value })}
                InputLabelProps={{ shrink: true }}
                fullWidth
                sx={{ input: { color: isDarkMode ? "#e0e0e0" : "#333" } }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: isDarkMode ? "#e0e0e0" : "#333" }}>Assigned To</InputLabel>
                <Select
                  value={newTask.assignedTo}
                  onChange={(e) => setNewTask({ ...newTask, assignedTo: e.target.value })}
                  sx={{ color: isDarkMode ? "#e0e0e0" : "#333" }}
                >
                  <MenuItem value="John Doe">John Doe</MenuItem>
                  <MenuItem value="Jane Smith">Jane Smith</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: isDarkMode ? "#e0e0e0" : "#333" }}>Priority</InputLabel>
                <Select
                  value={newTask.priority}
                  onChange={(e) => setNewTask({ ...newTask, priority: e.target.value })}
                  sx={{ color: isDarkMode ? "#e0e0e0" : "#333" }}
                >
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {newTask.status === "In Progress" && (
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Progress (%)"
                  type="number"
                  value={newTask.progress}
                  onChange={(e) => setNewTask({ ...newTask, progress: Math.min(100, Math.max(0, e.target.value)) })}
                  fullWidth
                  sx={{ input: { color: isDarkMode ? "#e0e0e0" : "#333" } }}
                />
              </Grid>
            )}
            <Grid item xs={12}>
              <Button variant="contained" onClick={editTaskId ? handleSaveEdit : handleAddTask}>
                {editTaskId ? "Save Changes" : "Add Task"}
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Search Bar */}
        <Box sx={{ mb: 3 }}>
          <TextField
            placeholder="Search tasks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{ startAdornment: <Search sx={{ color: isDarkMode ? "#b0bec5" : "#666" }} /> }}
            sx={{ width: "300px", input: { color: isDarkMode ? "#e0e0e0" : "#333" } }}
          />
        </Box>

        {/* Kanban Board */}
        <DragDropContext onDragEnd={handleDragEnd}>
          <Grid container spacing={3}>
            {["To Do", "In Progress", "Done"].map((status) => (
              <Grid item xs={12} sm={4} key={status}>
                <KanbanColumn>
                  <Typography variant="h6" sx={{ mb: 2, color: isDarkMode ? "#e0e0e0" : "#333" }}>
                    {status} ({tasks.filter((t) => t.status === status && filterTasks(t)).length})
                  </Typography>
                  <Droppable droppableId={status} key={status}>
                    {(provided) => (
                      <Box ref={provided.innerRef} {...provided.droppableProps}>
                        {tasks
                          .filter((task) => task.status === status && filterTasks(task))
                          .map((task, index) => (
                            <Draggable key={task.id} draggableId={task.id.toString()} index={index}>
                              {(provided) => (
                                <TaskCard
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  priority={task.priority}
                                >
                                  <CardContent>
                                    <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                                      <Typography variant="h6" sx={{ color: isDarkMode ? "#e0e0e0" : "#333" }}>
                                        {task.title}
                                      </Typography>
                                      <Box>
                                        <IconButton size="small" onClick={() => handleEditTask(task)}>
                                          <Edit sx={{ color: isDarkMode ? "#90caf9" : "primary.main" }} />
                                        </IconButton>
                                        <IconButton size="small" onClick={() => handleDeleteTask(task.id)}>
                                          <Delete sx={{ color: isDarkMode ? "#ef5350" : "error.main" }} />
                                        </IconButton>
                                      </Box>
                                    </Box>
                                    <Typography variant="body2" sx={{ color: isDarkMode ? "#b0bec5" : "#666", mb: 1 }}>
                                      {task.description}
                                    </Typography>
                                    <Typography variant="caption" sx={{ color: isDarkMode ? "#b0bec5" : "text.secondary" }}>
                                      Assigned: {task.assignedTo} | Deadline: {task.deadline}
                                    </Typography>
                                    <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                                      <Chip
                                        label={getDueStatus(task.deadline)}
                                        size="small"
                                        color={getDueStatus(task.deadline) === "Overdue" ? "error" : getDueStatus(task.deadline) === "Due Soon" ? "warning" : "success"}
                                      />
                                      <Chip label={task.priority} size="small" color={task.priority === "High" ? "error" : task.priority === "Medium" ? "warning" : "success"} />
                                    </Box>
                                    {task.status === "In Progress" && (
                                      <LinearProgress variant="determinate" value={task.progress} sx={{ mt: 1 }} />
                                    )}
                                  </CardContent>
                                </TaskCard>
                              )}
                            </Draggable>
                          ))}
                        {provided.placeholder}
                      </Box>
                    )}
                  </Droppable>
                </KanbanColumn>
              </Grid>
            ))}
          </Grid>
        </DragDropContext>
      </Box>
    </TasksContainer>
  );
};

export default Tasks;