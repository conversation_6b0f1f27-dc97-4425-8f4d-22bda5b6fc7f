import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  TextareaAutosize,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  CloudUpload as UploadIcon,
  GetApp as DownloadIcon,
  Visibility as ViewIcon,
  Schedule as ScheduleIcon,
  Edit as EditIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import <PERSON> from 'papaparse'; // For CSV parsing
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';

const HRContainer = styled(Box)(({ theme }) => ({
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, #1a2525 0%, #263238 100%)'
    : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
  minHeight: '100vh',
  display: 'flex',
}));

const SectionCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: '12px',
  background: theme.palette.mode === 'dark' ? '#263238' : '#fff',
  boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px rgba(33, 150, 243, 0.3)' : '0 2px 10px rgba(0, 0, 0, 0.1)',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
}));

const EmployeeCard = styled(Card)(({ theme }) => ({
  borderRadius: '12px',
  background: theme.palette.mode === 'dark' ? '#37474f' : '#fff',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.03)',
    boxShadow: theme.palette.mode === 'dark' ? '0 6px 20px rgba(33, 150, 243, 0.3)' : '0 4px 15px rgba(0, 0, 0, 0.15)',
  },
}));

const HR = () => {
  const [tabValue, setTabValue] = useState(0);
  const [employees, setEmployees] = useState([
    { id: 1, name: 'John Doe', email: '<EMAIL>', position: 'Software Engineer', salary: '$90,000', department: 'Engineering', picture: 'https://via.placeholder.com/150', role: 'Engineer' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', position: 'Product Manager', salary: '$110,000', department: 'Product', picture: 'https://via.placeholder.com/150', role: 'Manager' },
  ]);
  const [approvedCVs, setApprovedCVs] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [openProfileDialog, setOpenProfileDialog] = useState(false);
  const [jobDescription, setJobDescription] = useState('');
  const [cvLimit, setCvLimit] = useState(5);
  const [cvFiles, setCvFiles] = useState([]);
  const [filteredCVs, setFilteredCVs] = useState([]);
  const [performanceReviews, setPerformanceReviews] = useState({});

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // 1. CV Submission & ATS Filtering
  const handleCVUpload = (event) => {
    const files = Array.from(event.target.files);
    setCvFiles(files);
  };

  const filterCVsByATS = () => {
    const keywords = jobDescription.toLowerCase().split(' ');
    const scoredCVs = cvFiles.map((file, index) => {
      // Mock content extraction (in real app, use PDF parser like pdf.js)
      const mockContent = `${file.name} skills experience ${keywords.slice(0, 3).join(' ')}`; // Simulate CV content
      const score = keywords.reduce((acc, keyword) => acc + (mockContent.includes(keyword) ? 1 : 0), 0);
      return { id: index, name: file.name, score };
    }).sort((a, b) => b.score - a.score).slice(0, cvLimit);
    setFilteredCVs(scoredCVs);
    setApprovedCVs(scoredCVs.map(cv => ({ id: cv.id, name: cv.name.split('.')[0], email: `${cv.name.split('.')[0].toLowerCase()}@example.com`, position: 'Candidate', cv: cv.name })));
  };

  // 2. Add Employees via CSV/Excel
  const handleCSVUpload = (event) => {
    const file = event.target.files[0];
    Papa.parse(file, {
      header: true,
      complete: (result) => {
        const newEmployees = result.data.map((row, index) => ({
          id: employees.length + index + 1,
          name: row.name || 'Unknown',
          email: row.email || `${row.name.toLowerCase().replace(' ', '')}@example.com`,
          position: row.position || 'N/A',
          salary: row.salary || '$0',
          department: row.department || 'General',
          picture: row.picture || 'https://via.placeholder.com/150',
          role: row.role || 'Employee',
        }));
        setEmployees([...employees, ...newEmployees]);
        alert(`${newEmployees.length} employees added successfully`);
      },
      error: (error) => alert(`Error parsing CSV: ${error}`),
    });
  };

  // 3. Employee Details
  const handleViewProfile = (employee) => {
    setSelectedEmployee(employee);
    setOpenProfileDialog(true);
  };

  const handleCloseProfileDialog = () => {
    setOpenProfileDialog(false);
  };

  // 4. Performance Review
  const handlePerformanceReview = (employeeId, score, comment) => {
    setPerformanceReviews({ ...performanceReviews, [employeeId]: { score, comment } });
  };

  return (
    <HRContainer>
      <Sidebar />
      <Box sx={{ flexGrow: 1, marginLeft: '250px', p: 3 }}>
        <Navbar />
        <Typography variant="h4" sx={{ mb: 3, color: theme => theme.palette.text.primary }}>HR Management</Typography>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="CV Filtering" />
          <Tab label="Add Employees" />
          <Tab label="Employee Directory" />
          <Tab label="Performance Reviews" />
        </Tabs>

        {/* 1. CV Submission & ATS Filtering */}
        {tabValue === 0 && (
          <SectionCard>
            <Typography variant="h5" sx={{ mb: 2 }}>CV Filtering</Typography>
            <Box sx={{ mb: 3 }}>
              <TextareaAutosize
                minRows={3}
                placeholder="Enter job description..."
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
                style={{ width: '100%', padding: '8px', borderRadius: '4px', borderColor: '#ccc' }}
              />
              <TextField
                label="CV Limit"
                type="number"
                value={cvLimit}
                onChange={(e) => setCvLimit(Math.max(1, parseInt(e.target.value) || 1))}
                sx={{ mt: 2, width: '150px' }}
              />
              <Button
                variant="contained"
                component="label"
                startIcon={<UploadIcon />}
                sx={{ mt: 2, ml: 2 }}
              >
                Upload CVs
                <input type="file" multiple accept=".pdf" hidden onChange={handleCVUpload} />
              </Button>
              <Button
                variant="contained"
                onClick={filterCVsByATS}
                disabled={!cvFiles.length || !jobDescription}
                sx={{ mt: 2, ml: 2 }}
              >
                Filter CVs
              </Button>
            </Box>
            {filteredCVs.length > 0 && (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Score</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredCVs.map((cv) => (
                      <TableRow key={cv.id}>
                        <TableCell>{cv.name}</TableCell>
                        <TableCell>{cv.score}</TableCell>
                        <TableCell>
                          <Button startIcon={<DownloadIcon />} onClick={() => alert(`Downloading ${cv.name}`)}>
                            Download
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </SectionCard>
        )}

        {/* 2. Add Employees via CSV/Excel */}
        {tabValue === 1 && (
          <SectionCard>
            <Typography variant="h5" sx={{ mb: 2 }}>Add Employees</Typography>
            <Button
              variant="contained"
              component="label"
              startIcon={<UploadIcon />}
            >
              Upload CSV/Excel
              <input type="file" accept=".csv" hidden onChange={handleCSVUpload} />
            </Button>
            <Typography variant="body2" sx={{ mt: 2 }}>
              CSV Format: name,email,position,salary,department,picture,role
            </Typography>
          </SectionCard>
        )}

        {/* 3. Employee Directory */}
        {tabValue === 2 && (
          <SectionCard>
            <Typography variant="h5" sx={{ mb: 2 }}>Employee Directory</Typography>
            <Grid container spacing={2}>
              {employees.map((employee) => (
                <Grid item xs={12} sm={6} md={4} key={employee.id}>
                  <EmployeeCard>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar src={employee.picture} sx={{ width: 60, height: 60 }} />
                        <Box>
                          <Typography variant="h6">{employee.name}</Typography>
                          <Typography variant="body2" color="text.secondary">{employee.position}</Typography>
                          <Typography variant="body2" color="text.secondary">{employee.role}</Typography>
                        </Box>
                      </Box>
                    </CardContent>
                    <CardActions>
                      <Button size="small" startIcon={<ViewIcon />} onClick={() => handleViewProfile(employee)}>
                        Show Details
                      </Button>
                    </CardActions>
                  </EmployeeCard>
                </Grid>
              ))}
            </Grid>
          </SectionCard>
        )}

        {/* 4. Performance Reviews */}
        {tabValue === 3 && (
          <SectionCard>
            <Typography variant="h5" sx={{ mb: 2 }}>Performance Reviews</Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Score</TableCell>
                    <TableCell>Comment</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {employees.map((employee) => (
                    <TableRow key={employee.id}>
                      <TableCell>{employee.name}</TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={performanceReviews[employee.id]?.score || ''}
                          onChange={(e) => handlePerformanceReview(employee.id, e.target.value, performanceReviews[employee.id]?.comment || '')}
                          sx={{ width: '80px' }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          value={performanceReviews[employee.id]?.comment || ''}
                          onChange={(e) => handlePerformanceReview(employee.id, performanceReviews[employee.id]?.score || '', e.target.value)}
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton onClick={() => alert(`Review saved for ${employee.name}`)}>
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </SectionCard>
        )}
      </Box>

      {/* Employee Profile Dialog */}
      <Dialog open={openProfileDialog} onClose={handleCloseProfileDialog} fullWidth maxWidth="md">
        <DialogTitle>{selectedEmployee?.name}'s Profile</DialogTitle>
        <DialogContent>
          {selectedEmployee && (
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar src={selectedEmployee.picture} sx={{ width: 100, height: 100, mr: 2 }} />
                <Box>
                  <Typography variant="h6">{selectedEmployee.name}</Typography>
                  <Typography variant="body1">{selectedEmployee.email}</Typography>
                  <Typography variant="body1">{selectedEmployee.position}</Typography>
                  <Typography variant="body1">Role: {selectedEmployee.role}</Typography>
                  <Typography variant="body1">Department: {selectedEmployee.department}</Typography>
                </Box>
              </Box>
              <TextField label="Salary" value={selectedEmployee.salary} fullWidth disabled sx={{ mb: 2 }} />
              <Button variant="contained" startIcon={<EditIcon />} sx={{ mt: 2 }}>Modify</Button>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseProfileDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </HRContainer>
  );
};

export default HR;