package com.ppg.ems_server_side_v0.controller;

import com.ppg.ems_server_side_v0.domain.Address;
import com.ppg.ems_server_side_v0.domain.Person;
import com.ppg.ems_server_side_v0.domain.Role;
import com.ppg.ems_server_side_v0.domain.User;
import com.ppg.ems_server_side_v0.domain.enums.PersonType;
import com.ppg.ems_server_side_v0.model.api.request.UserDTO;

import com.ppg.ems_server_side_v0.model.api.response.UserResponse;
import com.ppg.ems_server_side_v0.repository.AddressRepository;
import com.ppg.ems_server_side_v0.repository.PersonRepository;
import com.ppg.ems_server_side_v0.repository.RoleRepository;
import com.ppg.ems_server_side_v0.repository.UserRepository;
import com.ppg.ems_server_side_v0.service.core.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/users")
@Slf4j
public class UserController {

    private final UserService userService;

    @PostMapping("/user/")
    public ResponseEntity<UserResponse> addUser(@RequestBody UserDTO userDTO) {

        return ResponseEntity.ok().body(this.userService.addUser(userDTO));

    }

    @PutMapping("/updateById/{id}")
    public ResponseEntity<UserResponse> updateUser(@RequestBody UserDTO userDTO, @PathVariable String id) {

        return ResponseEntity.ok(this.userService.updateUser(userDTO, id));

    }

    @DeleteMapping("/deleteById/{id}")
    public ResponseEntity<Void> deleteUserById(@PathVariable String id) {

        this.userService.deleteUserById(id);

        return ResponseEntity.ok().body(null);

    }

    @GetMapping("/findById/{id}")
    public ResponseEntity<UserResponse> findUserById(@PathVariable String id) {

        return ResponseEntity.ok(this.userService.findUserById(id));

    }

    @GetMapping("/findAll/")
    public ResponseEntity<List<UserResponse>> findAllUser() {

        return ResponseEntity.ok(this.userService.findAllUser());

    }

    @GetMapping("/findByEmail/1/{email}")
    public ResponseEntity<User> findUserByEmail(@PathVariable String email) {

        return ResponseEntity.ok(this.userService.findUserByEmail(email));

    }

    @GetMapping("/{userId}/stats")
    public ResponseEntity<Map<String, Object>> getUserStats(@PathVariable String userId) {
        try {
            log.info("Fetching stats for user: {}", userId);

            // Create mock user stats for now
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalTasks", 15);
            stats.put("completedTasks", 12);
            stats.put("pendingTasks", 3);
            stats.put("totalProjects", 5);
            stats.put("activeProjects", 3);
            stats.put("completedProjects", 2);
            stats.put("totalHours", 160);
            stats.put("thisMonthHours", 40);
            stats.put("efficiency", 85.5);
            stats.put("rating", 4.2);

            log.info("Returning stats for user: {}", userId);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error fetching user stats for user {}: {}", userId, e.getMessage(), e);
            return ResponseEntity.status(500).build();
        }
    }
}
