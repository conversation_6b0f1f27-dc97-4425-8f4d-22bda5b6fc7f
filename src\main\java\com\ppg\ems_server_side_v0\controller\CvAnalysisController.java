package com.ppg.ems_server_side_v0.controller;

import com.ppg.ems_server_side_v0.model.api.request.CvFilterRequest;
import com.ppg.ems_server_side_v0.model.api.response.CvAnalysisResponse;
import com.ppg.ems_server_side_v0.service.core.CvAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/hr/cv-analysis")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://127.0.0.1:3000"}, allowCredentials = "true")
public class CvAnalysisController {

    private final CvAnalysisService cvAnalysisService;

    @PostMapping
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<List<CvAnalysisResponse>> analyzeCvs(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "jobDescriptionId", required = false) String jobDescriptionId,
            @RequestParam(value = "jobDescriptionText", required = false) String jobDescriptionText,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit,
            @RequestParam(value = "keywordWeight", defaultValue = "0.6") Double keywordWeight,
            @RequestParam(value = "semanticWeight", defaultValue = "0.4") Double semanticWeight) {
        
        try {
            log.info("Analyzing {} CV files with limit: {}", files.size(), limit);
            
            // Validate input
            if (files.isEmpty()) {
                log.warn("No files provided for CV analysis");
                return ResponseEntity.badRequest().build();
            }
            
            if (jobDescriptionId == null && jobDescriptionText == null) {
                log.warn("No job description provided for CV analysis");
                return ResponseEntity.badRequest().build();
            }
            
            // Create filter request
            CvFilterRequest filterRequest = new CvFilterRequest(
                    jobDescriptionId,
                    jobDescriptionText,
                    limit,
                    keywordWeight,
                    semanticWeight
            );
            
            // Analyze CVs
            List<CvAnalysisResponse> analyses = cvAnalysisService.analyzeCvs(files, filterRequest);
            
            log.info("Successfully analyzed {} CVs, returning top {} results", 
                    analyses.size(), Math.min(analyses.size(), limit));
            
            // Return top results based on limit
            List<CvAnalysisResponse> topResults = analyses.stream()
                    .limit(limit)
                    .toList();
            
            return ResponseEntity.ok(topResults);
            
        } catch (Exception e) {
            log.error("Error analyzing CVs: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/job-description/{jobDescriptionId}")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<List<CvAnalysisResponse>> getCvAnalysesByJobDescription(
            @PathVariable String jobDescriptionId) {
        try {
            log.info("Fetching CV analyses for job description: {}", jobDescriptionId);
            
            List<CvAnalysisResponse> analyses = cvAnalysisService.getCvAnalysesByJobDescription(jobDescriptionId);
            
            log.info("Found {} CV analyses for job description: {}", analyses.size(), jobDescriptionId);
            return ResponseEntity.ok(analyses);
            
        } catch (Exception e) {
            log.error("Error fetching CV analyses for job description {}: {}", jobDescriptionId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{analysisId}/download")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<Resource> downloadCvFile(@PathVariable String analysisId) {
        try {
            log.info("Downloading CV file for analysis: {}", analysisId);
            
            byte[] fileData = cvAnalysisService.downloadCvFile(analysisId);
            
            ByteArrayResource resource = new ByteArrayResource(fileData);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"cv_" + analysisId + ".pdf\"")
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("Error downloading CV file for analysis {}: {}", analysisId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @PostMapping("/extract-text")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<String> extractTextFromPdf(@RequestParam("file") MultipartFile file) {
        try {
            log.info("Extracting text from PDF: {}", file.getOriginalFilename());
            
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            String extractedText = cvAnalysisService.extractTextFromPdf(file);
            
            log.info("Successfully extracted {} characters from PDF: {}", 
                    extractedText.length(), file.getOriginalFilename());
            
            return ResponseEntity.ok(extractedText);
            
        } catch (Exception e) {
            log.error("Error extracting text from PDF {}: {}", file.getOriginalFilename(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/calculate-score")
    @PreAuthorize("hasRole('HR') or hasRole('ADMIN')")
    public ResponseEntity<Double> calculateMatchingScore(
            @RequestParam("cvText") String cvText,
            @RequestParam("jobDescription") String jobDescription,
            @RequestParam(value = "scoreType", defaultValue = "keyword") String scoreType) {
        
        try {
            log.info("Calculating {} score for CV text ({} chars) against job description ({} chars)", 
                    scoreType, cvText.length(), jobDescription.length());
            
            Double score;
            switch (scoreType.toLowerCase()) {
                case "semantic":
                    score = cvAnalysisService.calculateSemanticScore(cvText, jobDescription);
                    break;
                case "keyword":
                default:
                    score = cvAnalysisService.calculateKeywordScore(cvText, jobDescription);
                    break;
            }
            
            log.info("Calculated {} score: {}", scoreType, score);
            return ResponseEntity.ok(score);
            
        } catch (Exception e) {
            log.error("Error calculating {} score: {}", scoreType, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("CV Analysis service is running");
    }
}
