import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Button, 
  LinearProgress,
  useTheme,
  styled,
  IconButton,
  Tooltip,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { 
  People as PeopleIcon, 
  <PERSON><PERSON>hart as BarChartIcon, 
  Inventory as InventoryIcon, 
  MonetizationOn as MoneyIcon,
  Refresh as RefreshIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Computer as ComputerIcon,
  Folder as FolderIcon,
  AttachMoney as AttachMoneyIcon,
  Mail as MailIcon,
  CalendarToday as CalendarIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';
import { faker } from '@faker-js/faker';

// Styled components
const DashboardContainer = styled(Box)(({ theme }) => ({
  background: theme.palette.mode === 'dark' 
    ? 'linear-gradient(135deg, #1a2525 0%, #2e3b3e 100%)'
    : 'linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%)',
  minHeight: '100vh',
  padding: theme.spacing(3),
}));

const MetricCard = styled(Card)(({ theme }) => ({
  transition: 'all 0.3s ease-in-out',
  borderRadius: '16px',
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)'
    : '#fff',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 8px 25px rgba(33, 150, 243, 0.3)'
      : theme.shadows[8],
  },
}));

const QuickActionButton = styled(Button)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(2),
  borderRadius: '16px',
  backgroundColor: theme.palette.mode === 'dark' ? '#2e3b3e' : '#ffffff',
  border: `1px solid ${theme.palette.mode === 'dark' ? '#455a64' : '#e0e0e0'}`,
  textTransform: 'none',
  color: theme.palette.text.primary,
  transition: 'all 0.3s ease',
  boxShadow: theme.palette.mode === 'dark' 
    ? '0 2px 10px rgba(0, 0, 0, 0.5)'
    : '0 2px 10px rgba(0, 0, 0, 0.05)',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? '#37474f' : '#f5f5f5',
    transform: 'scale(1.05)',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 6px 20px rgba(33, 150, 243, 0.3)'
      : '0 6px 20px rgba(0, 0, 0, 0.1)',
    borderColor: theme.palette.primary.main,
  },
  '& .MuiButton-startIcon': {
    margin: 0,
    marginBottom: theme.spacing(1),
    '& svg': {
      fontSize: 32,
      color: theme.palette.primary.main,
      transition: 'color 0.3s ease',
    },
  },
  '&:hover .MuiButton-startIcon svg': {
    color: theme.palette.primary.dark,
  },
}));

const ActivityCard = styled(Card)(({ theme }) => ({
  borderRadius: '12px',
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)'
    : '#fff',
  border: theme.palette.mode === 'dark' ? '1px solid #455a64' : 'none',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'scale(1.02)',
    boxShadow: theme.palette.mode === 'dark' 
      ? '0 6px 20px rgba(33, 150, 243, 0.3)'
      : theme.shadows[4],
  },
}));



const HomePage = () => {
  const theme = useTheme();
  const [metrics, setMetrics] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [recentActivities, setRecentActivities] = useState([]);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const generateMetrics = () => [
      { title: 'Total Employees', value: Math.floor(Math.random() * 2000) + 1000, icon: <PeopleIcon />, color: '#42a5f5', change: (Math.random() * 10 - 5).toFixed(1) },
      { title: 'Revenue', value: `$${(Math.random() * 5 + 1).toFixed(1)}M`, icon: <MoneyIcon />, color: '#66bb6a', change: (Math.random() * 15).toFixed(1) },
      { title: 'Inventory Items', value: Math.floor(Math.random() * 5000) + 5000, icon: <InventoryIcon />, color: '#ffa726', change: (Math.random() * 5 - 2.5).toFixed(1) },
      { title: 'Active Projects', value: Math.floor(Math.random() * 30) + 20, icon: <BarChartIcon />, color: '#ab47bc', change: (Math.random() * 8).toFixed(1) },
    ];

    const generateNotifications = () => [
      { id: 1, text: 'New IT ticket submitted', time: '2 mins ago', read: false },
      { id: 2, text: 'Calendar event scheduled', time: '15 mins ago', read: false },
      { id: 3, text: 'New employee added in HR', time: '1 hour ago', read: true },
      { id: 4, text: 'Finance report generated', time: '3 hours ago', read: true },
    ];

    const generateActivities = () => [
      { id: 1, user: 'John Doe', action: 'submitted an IT ticket', time: 'Just now', avatar: faker.image.avatar(), path: '/it' },
      { id: 2, user: 'Jane Smith', action: 'updated calendar event', time: '5 mins ago', avatar: faker.image.avatar(), path: '/calendar' },
      { id: 3, user: 'Mike Johnson', action: 'added new employee', time: '30 mins ago', avatar: faker.image.avatar(), path: '/hr' },
      { id: 4, user: 'Sarah Williams', action: 'completed task', time: '2 hours ago', avatar: faker.image.avatar(), path: '/tasks' },
    ];

    setMetrics(generateMetrics());
    setNotifications(generateNotifications());
    setRecentActivities(generateActivities());
  }, [refreshKey]);

  const handleRefresh = () => setRefreshKey(prev => prev + 1);
  const handleMenuClose = () => setAnchorEl(null);
  const handleNotificationRead = (id) => {
    setNotifications(notifications.map(n => n.id === id ? { ...n, read: true } : n));
  };

  const quickActions = [
    { text: 'HR', icon: <PeopleIcon />, path: '/hr' },
    { text: 'Finance', icon: <AttachMoneyIcon />, path: '/finance' },
    { text: 'IT', icon: <ComputerIcon />, path: '/it' },
    { text: 'File Management', icon: <FolderIcon />, path: '/file-management' },
    { text: 'Sales', icon: <AttachMoneyIcon />, path: '/sales' },
    { text: 'Mail', icon: <MailIcon />, path: '/mail' },
    { text: 'Calendar', icon: <CalendarIcon />, path: '/calendar' },
    { text: 'Tasks', icon: <AssignmentIcon />, path: '/tasks' },
  ];

  return (
    <Box sx={{ display: 'flex' }}>
      <Sidebar />
      <DashboardContainer sx={{ flexGrow: 1, marginLeft: { xs: 0, md: '250px' } }}>
        <Navbar />
        <Box sx={{ mt: 4 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4, flexWrap: 'wrap', gap: 2 }}>
            <Box>
              <Typography variant="h4" sx={{ color: theme.palette.text.primary, fontWeight: 700 }}>
                Dashboard Overview
              </Typography>
              <Typography variant="subtitle1" sx={{ color: theme.palette.text.secondary }}>
                {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Tooltip title="Refresh data">
                <IconButton onClick={handleRefresh}><RefreshIcon /></IconButton>
              </Tooltip>
              
              <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
                {notifications.map((notification) => (
                  <MenuItem key={notification.id} onClick={() => handleNotificationRead(notification.id)}>
                    <ListItemIcon><MailIcon color={!notification.read ? 'primary' : 'inherit'} /></ListItemIcon>
                    <ListItemText primary={notification.text} secondary={notification.time} />
                  </MenuItem>
                ))}
              </Menu>
            </Box>
          </Box>

          {/* Metrics Grid */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {metrics.map((metric, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <MetricCard>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ bgcolor: 'background.paper', p: 1, borderRadius: '12px' }}>
                        {React.cloneElement(metric.icon, { sx: { color: metric.color, fontSize: 24 } })}
                      </Box>
                      <Typography variant="body2" sx={{ color: metric.change > 0 ? '#66bb6a' : '#f06292' }}>
                        {metric.change > 0 ? <ArrowUpwardIcon sx={{ fontSize: 16 }} /> : <ArrowDownwardIcon sx={{ fontSize: 16 }} />}
                        {Math.abs(metric.change)}%
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ color: theme.palette.text.secondary }}>{metric.title}</Typography>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.text.primary }}>{metric.value}</Typography>
                    <LinearProgress variant="determinate" value={Math.random() * 100} sx={{ mt: 2, bgcolor: 'grey.300', '& .MuiLinearProgress-bar': { bgcolor: metric.color } }} />
                  </CardContent>
                </MetricCard>
              </Grid>
            ))}
          </Grid>

          {/* Charts Section */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={8}>
              <Card sx={{ borderRadius: '16px', background: theme.palette.mode === 'dark' ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' : '#fff' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: theme.palette.text.primary }}>Revenue Overview</Typography>
                  
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: '16px', background: theme.palette.mode === 'dark' ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' : '#fff' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: theme.palette.text.primary }}>Department Distribution</Typography>
                  
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Recent Activities */}
          <Card sx={{ borderRadius: '16px', mb: 4, background: theme.palette.mode === 'dark' ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)' : '#fff' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: theme.palette.text.primary }}>Recent Activities</Typography>
              <Grid container spacing={2}>
                {recentActivities.map((activity) => (
                  <Grid item xs={12} sm={6} key={activity.id}>
                    <ActivityCard>
                      <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar src={activity.avatar} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body1" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                            {activity.user}
                          </Typography>
                          <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
                            {activity.action}
                          </Typography>
                          <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                            {activity.time}
                          </Typography>
                        </Box>
                        <Button
                          component={Link}
                          to={activity.path}
                          variant="outlined"
                          size="small"
                          sx={{ textTransform: 'none' }}
                        >
                          View
                        </Button>
                      </CardContent>
                    </ActivityCard>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>

          {/* Updated Quick Actions */}
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3, color: theme.palette.text.primary }}>Quick Actions</Typography>
          <Grid container spacing={2}>
            {quickActions.map((action, index) => (
              <Grid item xs={6} sm={4} md={2} key={index}>
                <QuickActionButton
                  fullWidth
                  variant="contained"
                  startIcon={action.icon}
                  component={Link}
                  to={action.path}
                >
                  {action.text}
                </QuickActionButton>
              </Grid>
            ))}
          </Grid>
        </Box>
      </DashboardContainer>
    </Box>
  );
};

export default HomePage;