import React from 'react';
import Button from '@mui/material/Button'; // Material-UI Button
import 'bootstrap/dist/css/bootstrap.min.css'; // Bootstrap CSS

const ModifyButton = ({ onClick, label, color = 'primary', variant = 'contained', className = '' }) => {
  return (
    <Button
      variant={variant} // Material-UI variant (contained, outlined, etc.)
      color={color} // Material-UI color (primary, secondary, etc.)
      onClick={onClick} // Click handler
      className={`${className} m-2`} // Bootstrap margin and custom classes
    >
      {label}
    </Button>
  );
};

export default ModifyButton;