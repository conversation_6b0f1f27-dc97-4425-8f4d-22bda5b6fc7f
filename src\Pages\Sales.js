import React from 'react';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Avatar } from '@mui/material';
import Sidebar from '../components/Sidebar'; // Import your Sidebar component
import Navbar from '../components/Navbar'; // Import your Navbar component
import 'bootstrap/dist/css/bootstrap.min.css';

const Sales = () => {
  // Sample data for the CRM table
  const salesData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'Closed', amount: '$500' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'Pending', amount: '$300' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'Closed', amount: '$700' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', status: 'Pending', amount: '$200' },
  ];

  // Sample data for companies to work with
  const companiesData = [
    { id: 1, name: '<PERSON>Corp', logo: 'https://via.placeholder.com/50', industry: 'Technology', rating: 4.5 },
    { id: 2, name: 'HealthPlus', logo: 'https://via.placeholder.com/50', industry: 'Healthcare', rating: 4.2 },
    { id: 3, name: 'EcoGreen', logo: 'https://via.placeholder.com/50', industry: 'Sustainability', rating: 4.7 },
    { id: 4, name: 'AutoDrive', logo: 'https://via.placeholder.com/50', industry: 'Automotive', rating: 4.0 },
  ];

  // Sample data for influencers
  const influencersData = [
    { id: 1, name: 'Emma Watson', photo: 'https://via.placeholder.com/50', followers: '1.2M', reputation: 'High', niche: 'Lifestyle' },
    { id: 2, name: 'John Smith', photo: 'https://via.placeholder.com/50', followers: '800K', reputation: 'Medium', niche: 'Fitness' },
    { id: 3, name: 'Sophia Lee', photo: 'https://via.placeholder.com/50', followers: '2.5M', reputation: 'High', niche: 'Beauty' },
    { id: 4, name: 'Michael Brown', photo: 'https://via.placeholder.com/50', followers: '500K', reputation: 'Low', niche: 'Tech' },
  ];

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content Area */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' , marginLeft: '250px'}}>
        {/* Navbar */}
        <Navbar />

        {/* Main Content */}
        <Box sx={{ p: 3 }}>
          {/* Page Title */}
          <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: '#3f51b5' }}>
            Sales Dashboard
          </Typography>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Button variant="contained" color="primary">
              Add New Sale
            </Button>
            <Button variant="outlined" color="secondary">
              Export Data
            </Button>
          </Box>

          {/* Sales Data Table */}
          <Paper elevation={3} sx={{ p: 2, borderRadius: '12px', mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
              Sales Data
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>ID</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Amount</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {salesData.map((row) => (
                    <TableRow key={row.id}>
                      <TableCell>{row.id}</TableCell>
                      <TableCell>{row.name}</TableCell>
                      <TableCell>{row.email}</TableCell>
                      <TableCell>
                        <Typography
                          sx={{
                            color: row.status === 'Closed' ? '#4caf50' : '#f44336',
                            fontWeight: 'bold',
                          }}
                        >
                          {row.status}
                        </Typography>
                      </TableCell>
                      <TableCell>{row.amount}</TableCell>
                      <TableCell>
                        <Button variant="outlined" size="small" sx={{ mr: 1 }}>
                          Edit
                        </Button>
                        <Button variant="outlined" size="small" color="error">
                          Delete
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Companies Section */}
          <Paper elevation={3} sx={{ p: 2, borderRadius: '12px', mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
              Companies to Work With
            </Typography>
            <div className="row">
              {companiesData.map((company) => (
                <div key={company.id} className="col-md-3 mb-3">
                  <div className="card h-100">
                    <img
                      src={company.logo}
                      alt={company.name}
                      className="card-img-top"
                      style={{ height: '100px', objectFit: 'contain' }}
                    />
                    <div className="card-body">
                      <h5 className="card-title">{company.name}</h5>
                      <p className="card-text">
                        <strong>Industry:</strong> {company.industry}
                      </p>
                      <p className="card-text">
                        <strong>Rating:</strong> {company.rating} ⭐
                      </p>
                      <Button variant="contained" color="primary" fullWidth>
                        Contact
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Paper>

          {/* Influencers Section */}
          <Paper elevation={3} sx={{ p: 2, borderRadius: '12px', mb: 4 }}>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
              Influencers
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Photo</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Followers</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Reputation</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Niche</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {influencersData.map((influencer) => (
                    <TableRow key={influencer.id}>
                      <TableCell>
                        <Avatar src={influencer.photo} alt={influencer.name} />
                      </TableCell>
                      <TableCell>{influencer.name}</TableCell>
                      <TableCell>{influencer.followers}</TableCell>
                      <TableCell>
                        <Typography
                          sx={{
                            color: influencer.reputation === 'High' ? '#4caf50' : influencer.reputation === 'Medium' ? '#ff9800' : '#f44336',
                            fontWeight: 'bold',
                          }}
                        >
                          {influencer.reputation}
                        </Typography>
                      </TableCell>
                      <TableCell>{influencer.niche}</TableCell>
                      <TableCell>
                        <Button variant="outlined" size="small" sx={{ mr: 1 }}>
                          View Profile
                        </Button>
                        <Button variant="outlined" size="small" color="primary">
                          Contact
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
};

export default Sales;