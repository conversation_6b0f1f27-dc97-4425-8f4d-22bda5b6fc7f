import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import { useTheme } from '@mui/material/styles';
import Slide from '@mui/material/Slide';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const ConfirmationDialog = ({ open, onClose, onConfirm, title, message }) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Dialog
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      aria-labelledby="confirmation-dialog-title"
      aria-describedby="confirmation-dialog-description"
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '420px',
          borderRadius: '16px',
          boxShadow: '0px 8px 32px rgba(0, 0, 0, 0.2)',
          background: isDarkMode 
            ? 'linear-gradient(145deg, #2c3e50, #34495e)'
            : 'linear-gradient(145deg, #ffffff, #f5f5f5)',
          border: isDarkMode 
            ? '1px solid rgba(255, 255, 255, 0.1)'
            : '1px solid rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
        },
      }}
    >
      {/* Dialog Header */}
      <DialogTitle
        id="confirmation-dialog-title"
        sx={{
          fontWeight: '700',
          fontSize: '1.25rem',
          color: isDarkMode ? '#fff' : '#3f51b5',
          padding: '20px 24px',
          background: isDarkMode 
            ? 'rgba(0, 0, 0, 0.2)'
            : 'rgba(63, 81, 181, 0.08)',
          borderBottom: isDarkMode 
            ? '1px solid rgba(255, 255, 255, 0.1)'
            : '1px solid rgba(0, 0, 0, 0.1)',
        }}
      >
        {title}
      </DialogTitle>

      {/* Dialog Content */}
      <DialogContent sx={{ padding: '24px' }}>
        <DialogContentText
          id="confirmation-dialog-description"
          sx={{
            color: isDarkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
            fontSize: '0.95rem',
            lineHeight: '1.6',
          }}
        >
          {message}
        </DialogContentText>
      </DialogContent>

      {/* Dialog Actions */}
      <DialogActions sx={{ 
        padding: '16px 24px',
        background: isDarkMode 
          ? 'rgba(0, 0, 0, 0.2)'
          : 'rgba(0, 0, 0, 0.02)',
        borderTop: isDarkMode 
          ? '1px solid rgba(255, 255, 255, 0.1)'
          : '1px solid rgba(0, 0, 0, 0.1)',
      }}>
        {/* Cancel Button */}
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            textTransform: 'none',
            fontWeight: '600',
            padding: '8px 20px',
            borderRadius: '8px',
            color: isDarkMode ? '#fff' : '#3f51b5',
            borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(63, 81, 181, 0.5)',
            '&:hover': {
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : '#3f51b5',
              background: isDarkMode 
                ? 'rgba(255, 255, 255, 0.05)'
                : 'rgba(63, 81, 181, 0.04)',
            },
            transition: 'all 0.2s ease',
          }}
        >
          Cancel
        </Button>

        {/* Confirm Button */}
        <Button
          onClick={onConfirm}
          variant="contained"
          disableElevation
          sx={{
            textTransform: 'none',
            fontWeight: '600',
            padding: '8px 20px',
            borderRadius: '8px',
            color: '#fff',
            background: isDarkMode 
              ? 'linear-gradient(145deg, #ff5252, #f44336)'
              : 'linear-gradient(145deg, #3f51b5, #303f9f)',
            '&:hover': {
              background: isDarkMode 
                ? 'linear-gradient(145deg, #ff6b6b, #ff5252)'
                : 'linear-gradient(145deg, #5c6bc0, #3f51b5)',
              transform: 'translateY(-1px)',
            },
            transition: 'all 0.2s ease',
          }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationDialog;