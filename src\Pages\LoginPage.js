import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../Assets/ThemeContext';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import InputAdornment from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import Fade from '@mui/material/Fade';
import { authenticate } from '../API/authService'; 
import 'bootstrap/dist/css/bootstrap.min.css';

const LoginPage = () => {
  const { isDarkMode } = useTheme();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState({});
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const validateForm = () => {
    const newErrors = {};
    if (!email) newErrors.email = 'Email is required';
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) newErrors.email = 'Invalid email format';
    if (!password) newErrors.password = 'Password is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateForm()) {
      setIsSubmitting(true);
      try {
        const response = await authenticate({ email, password });
        // Store token in localStorage for future requests
        localStorage.setItem('token', response.token);
        // Redirect based on role
        if (response.role.role === 'ADMIN') {
          navigate('/admin/dashboard');
        } else {
          navigate('/home');
        }
      } catch (error) {
        setOpenSnackbar(true);
        // Provide more specific error message if available
        setErrors({ submit: error.message || error.error || 'Invalid email or password' });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      background: {
        default: isDarkMode ? '#121212' : '#f8f9fa',
        paper: isDarkMode ? '#1e1e1e' : '#ffffff',
      },
      primary: {
        main: isDarkMode ? '#90caf9' : '#4361ee',
      },
      secondary: {
        main: isDarkMode ? '#ff6b6b' : '#f72585',
      },
      text: {
        primary: isDarkMode ? '#ffffff' : '#212529',
        secondary: isDarkMode ? '#b0b0b0' : '#6c757d',
      },
    },
    shape: {
      borderRadius: 12,
    },
  });

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
    setErrors((prev) => ({ ...prev, submit: undefined }));
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      <Container
        component="main"
        maxWidth="sm"
        className="d-flex flex-column justify-content-center vh-100"
        sx={{ transition: 'all 0.3s ease' }}
      >
        <Fade in={true} timeout={800}>
          <Paper
            elevation={isDarkMode ? 0 : 6}
            sx={{
              padding: { xs: 3, sm: 4, md: 6 },
              borderRadius: '24px',
              backgroundColor: theme.palette.background.paper,
              border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
              boxShadow: isDarkMode ? '0 8px 32px rgba(0, 0, 0, 0.5)' : '0 8px 32px rgba(0, 0, 0, 0.1)',
              transform: 'translateY(0)',
              '&:hover': { transform: 'translateY(-2px)' },
              transition: 'all 0.3s ease',
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: '800',
                  background: isDarkMode ? 'linear-gradient(90deg, #90caf9, #64b5f6)' : 'linear-gradient(90deg, #4361ee, #3a0ca3)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: 1,
                }}
              >
                Welcome Back
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{ color: theme.palette.text.secondary, marginBottom: 3 }}
              >
                Sign in to continue to your account
              </Typography>
            </div>

            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="Email Address"
                variant="outlined"
                margin="normal"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                error={!!errors.email}
                helperText={errors.email}
                sx={{
                  marginBottom: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    '&.Mui-focused fieldset': {
                      borderColor: theme.palette.primary.main,
                      boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                    },
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailOutlinedIcon sx={{ color: errors.email ? theme.palette.error.main : theme.palette.text.secondary }} />
                    </InputAdornment>
                  ),
                }}
              />

              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                variant="outlined"
                margin="normal"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                error={!!errors.password}
                helperText={errors.password || errors.submit}
                sx={{
                  marginBottom: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    '&.Mui-focused fieldset': {
                      borderColor: theme.palette.primary.main,
                      boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                    },
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockOutlinedIcon sx={{ color: errors.password ? theme.palette.error.main : theme.palette.text.secondary }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                        sx={{ color: theme.palette.text.secondary, '&:hover': { color: theme.palette.primary.main } }}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                fullWidth
                variant="contained"
                color="primary"
                type="submit"
                disabled={isSubmitting}
                sx={{
                  marginTop: 2,
                  padding: '14px',
                  fontSize: '16px',
                  fontWeight: '700',
                  borderRadius: '12px',
                  textTransform: 'none',
                  letterSpacing: '0.5px',
                  background: isDarkMode ? 'linear-gradient(135deg, #90caf9, #64b5f6)' : 'linear-gradient(135deg, #4361ee, #3a0ca3)',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 6px 8px rgba(0, 0, 0, 0.15)',
                    background: isDarkMode ? 'linear-gradient(135deg, #64b5f6, #42a5f5)' : 'linear-gradient(135deg, #3a0ca3, #480ca8)',
                  },
                  '&:active': { transform: 'translateY(0)' },
                  transition: 'all 0.2s ease',
                }}
              >
                {isSubmitting ? 'Signing In...' : 'Sign In'}
              </Button>
            </form>

            <div style={{ textAlign: 'center', marginTop: '2rem' }}>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.secondary,
                  '& a': {
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    fontWeight: '600',
                    '&:hover': { textDecoration: 'underline' },
                  },
                }}
              >
                Don't have an account? <a href="/account-request">Inform IT Department</a>
              </Typography>
            </div>
          </Paper>
        </Fade>

        <Snackbar
          open={openSnackbar}
          autoHideDuration={4000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
          TransitionComponent={Fade}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={errors.submit ? 'error' : 'success'}
            sx={{
              width: '100%',
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              background: isDarkMode
                ? errors.submit ? 'linear-gradient(135deg, #d32f2f, #c62828)' : 'linear-gradient(135deg, #2e7d32, #1b5e20)'
                : errors.submit ? 'linear-gradient(135deg, #ef5350, #e57373)' : 'linear-gradient(135deg, #4caf50, #2e7d32)',
              color: '#fff',
              '& .MuiAlert-icon': { color: '#fff' },
            }}
          >
            {errors.submit || `Welcome back, ${email.split('@')[0]}! Ready to get started? 🚀`}
          </Alert>
        </Snackbar>
      </Container>
    </MuiThemeProvider>
  );
};

export default LoginPage;