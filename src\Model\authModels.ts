interface Address {
    id: string;
    streetName: string;
    zipCode: string;
    state: string;
    town: string;
  }
  
  interface Person {
    id: string;
    firstName: string;
    lastName: string;
    birthDate: string;
    phoneNumber: string;
    personType: string;
    address: Address;
  }
  
  interface Role {
    id: string;
    role: string;
  }
  
  interface User {
    id: string;
    email: string;
    role: Role;
    person: Person;
  }
  
  interface AuthenticationDTO {
    email: string;
    password: string;
  }
  
  interface AuthenticationResponse {
    token: string;
    userId: string;
    email: string;
    role: Role;
  }
  
  interface UserDTO {
    email: string;
    password: string;
    roleId: string;
    person: {
      firstName: string;
      lastName: string;
      birthDate: string;
      phoneNumber: string;
      personType: string;
      address: {
        streetName: string;
        zipCode: string;
        state: string;
        town: string;
      };
    };
  }
  
  interface UserResponse {
    id: string;
    email: string;
    role: Role;
    person: Person;
  }
  
  interface ErrorResponse {
    error: string;
  }