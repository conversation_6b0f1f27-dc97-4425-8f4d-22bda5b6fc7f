{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^3.2.1", "@dnd-kit/core": "^6.3.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@faker-js/faker": "^9.6.0", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@mui/icons-material": "^6.4.5", "@mui/material": "^6.4.11", "@mui/x-charts": "^8.0.0", "@mui/x-data-grid": "^7.28.3", "@mui/x-date-pickers": "^7.28.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.8", "classnames": "^2.5.1", "d3-drag": "^3.0.0", "d3-force": "^3.0.0", "d3-selection": "^3.0.0", "d3-shape": "^3.2.0", "d3-zoom": "^3.0.0", "frappe-gantt": "^1.0.3", "i18next": "^24.2.2", "moment": "^2.30.1", "papaparse": "^5.5.2", "postcss": "^8.5.3", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.17.1", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-d3-graph": "^2.6.0", "react-dom": "^18.3.1", "react-gantt-timeline": "^0.4.3", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "recharts": "^2.15.2", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.3", "typescript": "^5.7.3", "vis-network": "^9.1.9", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}