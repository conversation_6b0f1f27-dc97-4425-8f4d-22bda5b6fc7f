import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
} from '@mui/material';
import SavedSearchIcon from '@mui/icons-material/SavedSearch';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  GetApp as DownloadIcon,
} from '@mui/icons-material';
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';

const FileManagement = () => {
  const [files, setFiles] = useState([
    {
      id: 1,
      name: 'Project Plan.pdf',
      type: 'pdf',
      size: '2.3 MB',
      date: '2023-10-01',
      status: 'Pending Approval',
      versions: [{ version: 1, date: '2023-10-01' }],
    },
    {
      id: 2,
      name: 'Design Mockups.png',
      type: 'image',
      size: '1.5 MB',
      date: '2023-10-02',
      status: 'Approved',
      versions: [{ version: 1, date: '2023-10-02' }],
    },
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const handleUpload = (event) => {
    const uploadedFiles = Array.from(event.target.files);
    const newFiles = uploadedFiles.map((file, index) => ({
      id: files.length + index + 1,
      name: file.name,
      type: file.type.split('/')[1],
      size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
      date: new Date().toISOString().split('T')[0],
      status: 'Pending Approval',
      versions: [{ version: 1, date: new Date().toISOString().split('T')[0] }],
    }));
    setFiles([...files, ...newFiles]);
    setSnackbarMessage('File uploaded successfully!');
    setOpenSnackbar(true);
  };

  const handleDelete = (id) => {
    setFiles(files.filter((file) => file.id !== id));
    setOpenDeleteDialog(false);
    setSnackbarMessage('File deleted successfully!');
    setOpenSnackbar(true);
  };

  const handleApprove = (id) => {
    setFiles(
      files.map((file) =>
        file.id === id ? { ...file, status: 'Approved' } : file
      )
    );
    setSnackbarMessage('File approved successfully!');
    setOpenSnackbar(true);
  };

  const handleReject = (id) => {
    setFiles(
      files.map((file) =>
        file.id === id ? { ...file, status: 'Rejected' } : file
      )
    );
    setSnackbarMessage('File rejected successfully!');
    setOpenSnackbar(true);
  };

  const handleDownload = (id) => {
    const file = files.find((file) => file.id === id);
    alert(`Downloading ${file.name}`);
  };

  const handleOpenDeleteDialog = (id) => {
    setFileToDelete(id);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const filteredFiles = files.filter((file) =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Sidebar */}
      <Sidebar />

      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' , marginLeft: '250px'}}>
        {/* Navbar */}
        <Navbar />

        {/* Main content */}
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" sx={{ mb: 3 }}>
            File Management
          </Typography>

          {/* Search and Upload */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <TextField
              placeholder="Search files..."
              variant="outlined"
              size="small"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: <SavedSearchIcon sx={{ color: 'action.active', mr: 1 }} />,
              }}
            />
            <Button
              variant="contained"
              component="label"
              startIcon={<UploadIcon />}
            >
              Upload File
              <input type="file" hidden multiple onChange={handleUpload} />
            </Button>
          </Box>

          {/* File Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredFiles.map((file) => (
                  <TableRow key={file.id}>
                    <TableCell>{file.name}</TableCell>
                    <TableCell>{file.type}</TableCell>
                    <TableCell>{file.size}</TableCell>
                    <TableCell>{file.date}</TableCell>
                    <TableCell>{file.status}</TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleApprove(file.id)}>
                        <ApproveIcon color="success" />
                      </IconButton>
                      <IconButton onClick={() => handleReject(file.id)}>
                        <RejectIcon color="error" />
                      </IconButton>
                      <IconButton onClick={() => handleDownload(file.id)}>
                        <DownloadIcon color="primary" />
                      </IconButton>
                      <IconButton onClick={() => handleOpenDeleteDialog(file.id)}>
                        <DeleteIcon color="error" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete File</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this file?
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={() => handleDelete(fileToDelete)} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for Notifications */}
      <Snackbar
        open={openSnackbar}
        autoHideDuration={3000}
        onClose={() => setOpenSnackbar(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default FileManagement;