import React, { useState } from 'react';
import Button from '@mui/material/Button';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import { useNavigate } from 'react-router-dom';
import ConfirmationDialog from './ConfirmationDialog'; // Import the new dialog component

const LogoutButton = () => {
  const [open, setOpen] = useState(false); // State to control the dialog
  const navigate = useNavigate();

  // Open the dialog
  const handleClickOpen = () => {
    setOpen(true);
  };

  // Close the dialog
  const handleClose = () => {
    setOpen(false);
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('token'); // Remove the token from local storage
    navigate('/login'); // Navigate to the login page
    handleClose(); // Close the dialog
  };

  return (
    <>
      {/* Logout Button */}
      <Button
        variant="contained"
        color="error"
        startIcon={<PowerSettingsNewIcon />}
        sx={{
          borderRadius: '12px',
          textTransform: 'none',
          justifyContent: 'flex-start',
          pl: 8.5,
          py: 1,
          fontSize: '14px',
          fontWeight: 'bold',
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          backgroundColor: '#e0e0e0', // Default color
          color: '#000', // Text color
          '&:hover': {
            backgroundColor: '#bdbdbd', // Hover color
          },
        }}
        fullWidth
        onClick={handleClickOpen} // Open the dialog on click
      >
        Logout
      </Button>

      {/* Reusable Confirmation Dialog */}
      <ConfirmationDialog
        open={open}
        onClose={handleClose}
        onConfirm={handleLogout}
        title="Confirm Logout"
        message="Are you sure you want to logout?"
      />
    </>
  );
};

export default LogoutButton;