server:
  port: 8088

spring:
  datasource:
    url: **************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true
        show_sql: false
    database: postgresql
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

app:
  cv:
    upload:
      dir: uploads/cvs
  files:
    upload:
      dir: uploads/documents
      max-size: 10MB
logging:
  level:
    org:
      springframework: info