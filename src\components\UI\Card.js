import React from 'react';
import classNames from 'classnames';

// Card root container
export const Card = ({ children, className, ...props }) => {
  return (
    <div
      className={classNames(
        'rounded-2xl border border-gray-200 bg-white shadow-sm',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// Card content area
export const CardContent = ({ children, className, ...props }) => {
  return (
    <div className={classNames('p-4', className)} {...props}>
      {children}
    </div>
  );
};
