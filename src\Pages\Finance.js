import React from 'react';
import { <PERSON>, Grid, Card, CardContent, Typography, Button } from '@mui/material';
import Sidebar from '../components/Sidebar';
import Navbar from '../components/Navbar';
import 'bootstrap/dist/css/bootstrap.min.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const Finance = () => {
  // Sample data for recent transactions
  const recentTransactions = [
    { id: 1, date: '2023-10-01', description: 'Office Supplies', amount: '$500', type: 'Expense' },
    { id: 2, date: '2023-10-02', description: 'Client Payment', amount: '$2000', type: 'Income' },
    { id: 3, date: '2023-10-03', description: 'Software Subscription', amount: '$100', type: 'Expense' },
    { id: 4, date: '2023-10-04', description: 'Consulting Fee', amount: '$1500', type: 'Income' },
  ];

  // Data for Revenue Trends (Bar Chart)
  const revenueData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
    datasets: [
      {
        label: 'Revenue',
        data: [5000, 7000, 6000, 8000, 9000, 10000, 12000, 11000, 13000, 15000],
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Data for Expense Breakdown (Pie Chart)
  const expenseData = {
    labels: ['Office Supplies', 'Software', 'Salaries', 'Utilities', 'Marketing'],
    datasets: [
      {
        label: 'Expenses',
        data: [500, 1000, 8000, 1200, 1500],
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 206, 86, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(153, 102, 255, 0.6)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Options for the Bar Chart
  const barOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Monthly Revenue Trends',
      },
    },
  };

  // Options for the Pie Chart
  const pieOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Expense Breakdown',
      },
    },
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* Sidebar */}
      <Sidebar />

      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', marginLeft: '250px' }}>
        {/* Navbar */}
        <Navbar />

        {/* Main content */}
        <Box sx={{ p: 3 }}>
          {/* Page Title */}
          <Typography variant="h4" gutterBottom>
            Welcome to the Finance Dashboard
          </Typography>

          {/* Key Metrics */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'primary.main', color: 'white' }}>
                <CardContent>
                  <Typography variant="h6">Total Revenue</Typography>
                  <Typography variant="h5">$50,000</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'success.main', color: 'white' }}>
                <CardContent>
                  <Typography variant="h6">Total Expenses</Typography>
                  <Typography variant="h5">$20,000</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'info.main', color: 'white' }}>
                <CardContent>
                  <Typography variant="h6">Net Profit</Typography>
                  <Typography variant="h5">$30,000</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ bgcolor: 'warning.main', color: 'white' }}>
                <CardContent>
                  <Typography variant="h6">Pending Invoices</Typography>
                  <Typography variant="h5">5</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Quick Actions */}
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Button variant="contained" color="primary" sx={{ mr: 2 }}>
                Add Transaction
              </Button>
              <Button variant="contained" color="success" sx={{ mr: 2 }}>
                Generate Report
              </Button>
              <Button variant="contained" color="info">
                View Analytics
              </Button>
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Transactions
              </Typography>
              <Box sx={{ overflowX: 'auto' }}>
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>Date</th>
                      <th>Description</th>
                      <th>Amount</th>
                      <th>Type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentTransactions.map((transaction) => (
                      <tr key={transaction.id}>
                        <td>{transaction.date}</td>
                        <td>{transaction.description}</td>
                        <td>{transaction.amount}</td>
                        <td>
                          <span
                            className={`badge ${
                              transaction.type === 'Income' ? 'bg-success' : 'bg-danger'
                            }`}
                          >
                            {transaction.type}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </Box>
            </CardContent>
          </Card>

          {/* Charts */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Revenue Trends
                  </Typography>
                  <Bar data={revenueData} options={barOptions} />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Expense Breakdown
                  </Typography>
                  <Pie data={expenseData} options={pieOptions} />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </Box>
  );
};

export default Finance;