import React from 'react';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { useTheme } from './Assets/ThemeContext';
import LoginPage from './Pages/LoginPage';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import HomePage from './Pages/HomePage';
import HR from './Pages/HR';
import { I18nextProvider } from 'react-i18next';
import i18n from './Assets/i18n';
import Finance from './Pages/Finance';
import IT from './Pages/IT';
import FileManagement from './Pages/FileManagement';
import Sales from './Pages/Sales';
import Mail from './Pages/Mail';
import Calendar from './Pages/Calendar';
import Tasks from './Pages/Tasks';
import SettingsButton from './Assets/SettingsButton';
import AccountRequestForm from './Pages/AccountRequestForm';
import { SettingsProvider } from './context/SettingsContext';
import AdminLayout from './ADMIN/Components/AdminLayout';
import AdminDashboard from './ADMIN/Pages/Dashboard';
import Users from './ADMIN/Pages/Users';
import ProtectedRoute from './Security/ProtectedRoute';

const AppContent = () => {
  const location = useLocation();
  const isLoginPage = location.pathname === '/';

  return (
    <>
      {!isLoginPage && <SettingsButton />}
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LoginPage />} />
        <Route path="/account-request" element={<AccountRequestForm />} />

        {/* Protected Routes */}
        <Route element={<ProtectedRoute />}>
          <Route path="/home" element={<HomePage />} />
          <Route path="/hr" element={<HR />} />
          <Route path="/finance" element={<Finance />} />
          <Route path="/it" element={<IT />} />
          <Route path="/file-management" element={<FileManagement />} />
          <Route path="/sales" element={<Sales />} />
          <Route path="/mail" element={<Mail />} />
          <Route path="/calendar" element={<Calendar />} />
          <Route path="/tasks" element={<Tasks />} />

          {/* Admin Routes */}
          <Route path="/admin" element={<AdminLayout />}>
            <Route index element={<AdminDashboard />} />
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="users" element={<Users />} />
          </Route>

          {/* Catch-all route for unmatched paths */}
          <Route path="*" element={<Navigate to="/home" />} />
        </Route>
      </Routes>
    </>
  );
};

const App = () => {
  const { isDarkMode } = useTheme();

  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      background: {
        default: isDarkMode ? '#1a2525' : '#f5f5f5',
        paper: isDarkMode ? '#263238' : '#fff',
      },
      primary: {
        main: isDarkMode ? '#42a5f5' : '#2196f3',
      },
      text: {
        primary: isDarkMode ? '#e0e0e0' : '#333',
        secondary: isDarkMode ? '#b0bec5' : '#666',
      },
    },
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: '16px',
            background: isDarkMode
              ? 'linear-gradient(145deg, #263238 0%, #37474f 100%)'
              : 'linear-gradient(145deg, #ffffff 0%, #f5f5f5 100%)',
            border: isDarkMode ? '1px solid #455a64' : 'none',
            transition: 'all 0.3s ease-in-out',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: isDarkMode
                ? '0 8px 25px rgba(33, 150, 243, 0.3)'
                : '0 8px 25px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: '12px',
            textTransform: 'none',
            fontWeight: 600,
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-3px)',
              boxShadow: isDarkMode
                ? '0 6px 20px rgba(33, 150, 243, 0.5)'
                : '0 6px 20px rgba(0, 0, 0, 0.2)',
            },
          },
        },
      },
      MuiIconButton: {
        styleOverrides: {
          root: {
            backgroundColor: isDarkMode ? '#37474f' : 'background.paper',
            color: isDarkMode ? '#90caf9' : 'inherit',
            '&:hover': {
              backgroundColor: isDarkMode ? '#455a64' : 'action.hover',
            },
          },
        },
      },
    },
  });

  return (
    <SettingsProvider>
      <I18nextProvider i18n={i18n}>
        <MuiThemeProvider theme={theme}>
          <CssBaseline />
          <Router>
            <AppContent />
          </Router>
        </MuiThemeProvider>
      </I18nextProvider>
    </SettingsProvider>
  );
};

export default App;