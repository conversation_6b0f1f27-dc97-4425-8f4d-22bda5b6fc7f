import React, { useState } from 'react';
import {
  AppBar,
  <PERSON>lbar,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Box,
  Badge,
  Stack,
  alpha,
  styled,
  InputBase,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  Mail as MessagesIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Help as HelpIcon,
} from '@mui/icons-material';
import { useTheme } from '../../Assets/ThemeContext';

// Styled Components
const ModernAppBar = styled(AppBar)(({ theme, isdarkmode }) => ({
  backgroundColor: isdarkmode === 'true' ? theme.palette.background.paper : theme.palette.primary.main,
  color: theme.palette.getContrastText(isdarkmode === 'true' ? theme.palette.background.paper : theme.palette.primary.main),
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  borderBottom: `1px solid ${isdarkmode === 'true' ? theme.palette.divider : alpha(theme.palette.common.white, 0.2)}`,
  width: { sm: `calc(100% - 250px)` },
  left: { sm: '250px' },
  position: 'fixed',
  zIndex: theme.zIndex.drawer + 1,
  transition: 'all 0.3s ease-in-out',
}));

const SearchBar = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create('width'),
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      width: '20ch',
      '&:focus': {
        width: '30ch',
      },
    },
  },
}));

const AdminNavbar = ({ toggleSidebar }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const { isDarkMode, toggleTheme } = useTheme();
  
  const user = {
    name: 'Admin User',
    email: '<EMAIL>',
    avatar: '/static/images/avatar/admin.jpg',
    role: 'System Administrator',
  };

  const handleMenuOpen = (event) => setAnchorEl(event.currentTarget);
  const handleMenuClose = () => setAnchorEl(null);

  return (
    <>
      <ModernAppBar position="fixed" isdarkmode={isDarkMode.toString()} marginLeft='250px'>
        <Toolbar sx={{ justifyContent: 'space-between', px: 2, minHeight: '64px' }}>
          {/* Left Section */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              onClick={toggleSidebar}
              sx={{ mr: 1, display: { sm: 'none' } }}
              edge="start"
            >
              <MenuIcon />
            </IconButton>
            
            <Box sx={{ position: 'relative', display: 'flex', alignItems: 'center' }}>
              <SearchBar
                placeholder="Search…"
                inputProps={{ 'aria-label': 'search' }}
                sx={{ ml: 1, backgroundColor: alpha('#fff', 0.15), borderRadius: 2 }}
              />
              <IconButton
                type="button"
                sx={{ p: '10px', position: 'absolute', left: 0 }}
                aria-label="search"
              >
                <SearchIcon />
              </IconButton>
            </Box>
          </Box>

          {/* Right Section */}
          <Stack direction="row" spacing={2} alignItems="center">
            <Tooltip title="Toggle Theme">
              <IconButton color="inherit" onClick={toggleTheme}>
                {isDarkMode ? <LightModeIcon /> : <DarkModeIcon />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Messages">
              <IconButton color="inherit">
                <Badge badgeContent={3} color="error">
                  <MessagesIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Notifications">
              <IconButton color="inherit">
                <Badge badgeContent={5} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Help & Support">
              <IconButton color="inherit" href="/help" target="_blank">
                <HelpIcon />
              </IconButton>
            </Tooltip>
            
            <IconButton onClick={handleMenuOpen}>
              <Avatar src={user.avatar} sx={{ width: 36, height: 36 }} />
            </IconButton>
          </Stack>
        </Toolbar>
      </ModernAppBar>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        PaperProps={{
          sx: {
            width: 300,
            mt: 1.5,
            p: 1,
            borderRadius: 2,
            boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
            backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          }
        }}
      >
        <Box sx={{ p: 2, textAlign: 'center', color: isDarkMode ? '#fff' : '#000' }}>
          <Avatar src={user.avatar} sx={{ width: 80, height: 80, mb: 1, mx: 'auto' }} />
          <Typography variant="h6">{user.name}</Typography>
          <Typography variant="body2" color={isDarkMode ? 'text.secondary' : 'text.primary'}>
            {user.email}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {user.role}
          </Typography>
        </Box>
        
        <Divider sx={{ my: 1, borderColor: isDarkMode ? '#333' : '#ddd' }} />
        
        <MenuItem onClick={handleMenuClose} sx={{ color: isDarkMode ? '#fff' : '#000' }}>
          <ListItemIcon><PersonIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Profile Settings</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={handleMenuClose} sx={{ color: isDarkMode ? '#fff' : '#000' }}>
          <ListItemIcon><SettingsIcon fontSize="small" /></ListItemIcon>
          <ListItemText>System Settings</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={handleMenuClose} sx={{ color: isDarkMode ? '#fff' : '#000' }}>
          <ListItemIcon><HelpIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Help Center</ListItemText>
        </MenuItem>
        
        <Divider sx={{ my: 1, borderColor: isDarkMode ? '#333' : '#ddd' }} />
        
        <MenuItem onClick={handleMenuClose} sx={{ color: isDarkMode ? '#fff' : '#000' }}>
          <ListItemIcon><LogoutIcon fontSize="small" color="error" /></ListItemIcon>
          <ListItemText primary="Logout" />
        </MenuItem>
      </Menu>
    </>
  );
};

export default AdminNavbar;