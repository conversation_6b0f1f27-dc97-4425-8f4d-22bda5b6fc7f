import React from 'react';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import 'bootstrap/dist/css/bootstrap.min.css'; // Bootstrap CSS

const CustomDropdown = ({ label, value, onChange, options, className = '' }) => {
  return (
    <FormControl fullWidth className={`mb-3 ${className}`}>
      <InputLabel>{label}</InputLabel>
      <Select value={value} onChange={onChange} label={label}>
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default CustomDropdown;