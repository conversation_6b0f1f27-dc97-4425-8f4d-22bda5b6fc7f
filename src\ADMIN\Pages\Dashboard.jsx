import React from 'react';
import { 
  Box, 
  Grid, 
  Typography, 
  Card, 
  CardContent, 
  Divider, 
  LinearProgress,
  Avatar,
  IconButton,
  Paper,
  styled,
  useTheme
} from '@mui/material';
import {
  Person as PersonIcon,
  <PERSON>Upward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Event as EventIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Security as SecurityIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from '@mui/icons-material';
import { alpha } from '@mui/material/styles';


const BarChart = ({ data, labels, colors }) => {
  const theme = useTheme();
  const maxValue = Math.max(...data);
  
  return (
    <Box sx={{ height: 250, display: 'flex', alignItems: 'flex-end', p: 2 }}>
      {data.map((value, index) => (
        <Box key={index} sx={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center',
          mx: 0.5
        }}>
          <Typography variant="caption" sx={{ mb: 1 }}>
            {labels[index]}
          </Typography>
          <Box sx={{
            width: '70%',
            height: `${(value / maxValue) * 100}%`,
            backgroundColor: colors ? colors[index] : theme.palette.primary.main,
            borderRadius: '4px 4px 0 0',
            transition: 'height 0.5s ease',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-end'
          }}>
            <Typography variant="caption" sx={{ 
              color: 'common.white', 
              mb: 0.5,
              textShadow: '0 1px 2px rgba(0,0,0,0.3)'
            }}>
              {value}
            </Typography>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

const LineChart = ({ data, labels }) => {
  const theme = useTheme();
  const maxValue = Math.max(...data);
  const minValue = Math.min(...data);
  const range = maxValue - minValue;
  
  return (
    <Box sx={{ 
      height: 250, 
      p: 3,
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: '1px',
        backgroundColor: theme.palette.divider
      }
    }}>
      <Box sx={{ 
        height: '100%', 
        width: '100%', 
        position: 'relative',
        display: 'flex'
      }}>
        {data.map((value, index) => (
          <React.Fragment key={index}>
            <Box sx={{
              position: 'absolute',
              bottom: `${((value - minValue) / range) * 90}%`,
              left: `${(index / (data.length - 1)) * 100}%`,
              width: 10,
              height: 10,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              transform: 'translateX(-50%)',
              zIndex: 2,
              '&::after': {
                content: '""',
                position: 'absolute',
                width: 16,
                height: 16,
                borderRadius: '50%',
                backgroundColor: alpha(theme.palette.primary.main, 0.2),
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)'
              }
            }} />
            {index > 0 && (
              <Box sx={{
                position: 'absolute',
                bottom: `${((data[index - 1] - minValue) / range) * 90}%`,
                left: `${((index - 1) / (data.length - 1)) * 100}%`,
                width: `${100 / (data.length - 1)}%`,
                height: 2,
                backgroundColor: theme.palette.primary.main,
                transformOrigin: 'left center',
                transform: `rotate(${Math.atan2(
                  ((value - data[index - 1]) / range) * 90,
                  100 / (data.length - 1)
        )}rad)`,
                zIndex: 1
              }} />
            )}
          </React.Fragment>
        ))}
      </Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between',
        mt: 1
      }}>
        {labels.map((label, index) => (
          <Typography key={index} variant="caption">
            {label}
          </Typography>
        ))}
      </Box>
    </Box>
  );
};

const PieChart = ({ data, colors }) => {
  const theme = useTheme();
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let cumulativePercent = 0;
  
  return (
    <Box sx={{ 
      width: 200, 
      height: 200, 
      position: 'relative',
      mx: 'auto',
      my: 3
    }}>
      <svg viewBox="0 0 100 100" style={{ width: '100%', height: '100%' }}>
        {data.map((item, index) => {
          const percent = item.value / total;
          const startX = 50 + 50 * Math.cos(2 * Math.PI * cumulativePercent);
          const startY = 50 + 50 * Math.sin(2 * Math.PI * cumulativePercent);
          cumulativePercent += percent;
          const endX = 50 + 50 * Math.cos(2 * Math.PI * cumulativePercent);
          const endY = 50 + 50 * Math.sin(2 * Math.PI * cumulativePercent);
          
          const largeArcFlag = percent > 0.5 ? 1 : 0;
          
          return [
            <path
              key={`path-${index}`}
              d={`M 50 50 L ${startX} ${startY} A 50 50 0 ${largeArcFlag} 1 ${endX} ${endY} Z`}
              fill={colors ? colors[index] : theme.palette.primary.main}
              stroke="#fff"
              strokeWidth="0.5"
            />,
            <text
              key={`text-${index}`}
              x={50 + 30 * Math.cos(2 * Math.PI * (cumulativePercent - percent / 2))}
              y={50 + 30 * Math.sin(2 * Math.PI * (cumulativePercent - percent / 2))}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="#fff"
              fontSize="5"
              fontWeight="bold"
            >
              {Math.round(percent * 100)}%
            </text>
          ];
        })}
        <circle cx="50" cy="50" r="30" fill="#fff" />
      </svg>
      <Box sx={{ 
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        textAlign: 'center'
      }}>
        <Typography variant="h6" fontWeight="bold">
          {total}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Total
        </Typography>
      </Box>
    </Box>
  );
};

// Styled Components
const StatCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '12px',
  boxShadow: theme.shadows[3],
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.shadows[6],
  },
}));

const ChartCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: '12px',
  height: '100%',
  boxShadow: theme.shadows[2],
  display: 'flex',
  flexDirection: 'column'
}));

const AdminDashboard = () => {
  const theme = useTheme();
  
  // Sample data
  const stats = [
    { 
      title: 'Total Users', 
      value: '1,024', 
      icon: <PersonIcon color="primary" />,
      change: '+12%',
      trend: 'up'
    },
    { 
      title: 'New This Week', 
      value: '56', 
      icon: <EventIcon color="secondary" />,
      change: '+5%',
      trend: 'up'
    },
    { 
      title: 'Active Users', 
      value: '89%', 
      icon: <TrendingUpIcon color="success" />,
      change: '+3%',
      trend: 'up'
    },
    { 
      title: 'Pending Actions', 
      value: '24', 
      icon: <SecurityIcon color="warning" />,
      change: '-2%',
      trend: 'down'
    }
  ];

  const userGrowthData = [120, 190, 170, 220, 260, 300, 350];
  const userGrowthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'];

  const trafficSources = [
    { name: 'Direct', value: 35, color: theme.palette.primary.main },
    { name: 'Social', value: 25, color: theme.palette.secondary.main },
    { name: 'Referral', value: 20, color: theme.palette.success.main },
    { name: 'Organic', value: 15, color: theme.palette.warning.main },
    { name: 'Email', value: 5, color: theme.palette.error.main }
  ];

  const activityData = [12, 19, 3, 5, 2, 3];
  const activityLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <Box sx={{ 
      p: 3, 
      minHeight: '100vh',
      backgroundColor: theme.palette.background.default
    }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 4 
      }}>
        <Typography variant="h4" fontWeight="bold"
          sx={{
            color: '#2c3e50',
            textShadow: '1px 1px 3px rgba(0, 0, 0, 0.3)',
            letterSpacing: 1.2,
          }}
        >
          Admin Dashboard
        </Typography>
        <IconButton>
          <RefreshIcon />
        </IconButton>
      </Box>
      
      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ 
                  bgcolor: alpha(theme.palette[stat.icon.props.color].main, 0.1),
                  color: theme.palette[stat.icon.props.color].main,
                  mr: 2
                }}>
                  {stat.icon}
                </Avatar>
                  <Typography variant="subtitle2" color="text.secondary">
                    {stat.title}
                  </Typography>
                </Box>
                <Typography variant="h3" component="div" fontWeight="bold">
                  {stat.value}
                </Typography>
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  mt: 1,
                  color: stat.trend === 'up' ? 'success.main' : 'error.main'
                }}>
                  {stat.trend === 'up' ? 
                    <ArrowUpwardIcon fontSize="small" /> : 
                    <ArrowDownwardIcon fontSize="small" />
                  }
                  <Typography variant="body2" sx={{ ml: 0.5 }}>
                    {stat.change} from last week
                  </Typography>
                </Box>
              </CardContent>
            </StatCard>
          </Grid>
        ))}
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3}>
        {/* User Growth Line Chart */}
        <Grid item xs={12} md={8}>
          <ChartCard>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mb: 2 
            }}>
              <Typography variant="h6" fontWeight="bold">
                User Growth
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LineChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Last 7 months
                </Typography>
              </Box>
            </Box>
            <Divider sx={{ mb: 2 }} />
            <LineChart data={userGrowthData} labels={userGrowthLabels} />
          </ChartCard>
        </Grid>

        {/* Traffic Sources Pie Chart */}
        <Grid item xs={12} md={4}>
          <ChartCard>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mb: 2 
            }}>
              <Typography variant="h6" fontWeight="bold">
                Traffic Sources
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PieChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  This month
                </Typography>
              </Box>
            </Box>
            <Divider sx={{ mb: 2 }} />
            <PieChart 
              data={trafficSources.map(item => ({ value: item.value }))} 
              colors={trafficSources.map(item => item.color)}
            />
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              justifyContent: 'center',
              mt: -2
            }}>
              {trafficSources.map((item, index) => (
                <Box key={index} sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  m: 1,
                  width: '45%'
                }}>
                  <Box sx={{
                    width: 12,
                    height: 12,
                    borderRadius: '2px',
                    backgroundColor: item.color,
                    mr: 1
                  }} />
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    {item.name}
                  </Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {item.value}%
                  </Typography>
                </Box>
              ))}
            </Box>
          </ChartCard>
        </Grid>

        {/* Weekly Activity Bar Chart */}
        <Grid item xs={12} md={6}>
          <ChartCard>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mb: 2 
            }}>
              <Typography variant="h6" fontWeight="bold">
                Weekly Activity
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <BarChartIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Last week
                </Typography>
              </Box>
            </Box>
            <Divider sx={{ mb: 2 }} />
            <BarChart 
              data={activityData} 
              labels={activityLabels}
              colors={[
                theme.palette.primary.main,
                theme.palette.secondary.main,
                theme.palette.success.main,
                theme.palette.warning.main,
                theme.palette.error.main,
                theme.palette.info.main
              ]}
            />
          </ChartCard>
        </Grid>

        {/* System Status */}
        <Grid item xs={12} md={6}>
          <ChartCard>
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              mb: 2 
            }}>
              <Typography variant="h6" fontWeight="bold">
                System Status
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AssessmentIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Live metrics
                </Typography>
              </Box>
            </Box>
            <Divider sx={{ mb: 2 }} />
            <Box sx={{ p: 2 }}>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>CPU Usage</Typography>
                  <Typography>65%</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={65} 
                  color={65 > 80 ? 'error' : 'primary'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Memory Usage</Typography>
                  <Typography>48%</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={48} 
                  color={48 > 80 ? 'error' : 'primary'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Storage Usage</Typography>
                  <Typography>82%</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={82} 
                  color={82 > 80 ? 'error' : 'primary'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Network Usage</Typography>
                  <Typography>91%</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={91} 
                  color={91 > 80 ? 'error' : 'primary'}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </Box>
          </ChartCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminDashboard;