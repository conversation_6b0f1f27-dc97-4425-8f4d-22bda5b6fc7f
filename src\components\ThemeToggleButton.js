import React from 'react';
import Button from '@mui/material/Button';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import { useTheme } from '../Assets/ThemeContext'; // Adjust the import path as needed

const ThemeToggleButton = () => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <Button
      variant="contained"
      color="primary"
      onClick={toggleTheme}
      startIcon={isDarkMode ? <Brightness7Icon /> : <Brightness4Icon />}
      sx={{
        backgroundColor: isDarkMode ? '#34495e' : '#f5f5f5', // Set color based on theme
        color: isDarkMode ? '#fff' : '#000', // Text color
        '&:hover': {
          backgroundColor: isDarkMode ? '#2c3e50' : '#e0e0e0', // Hover color
        },
      }}
    >
      {isDarkMode ? 'Light' : 'Dark'}
    </Button>
  );
};

export default ThemeToggleButton;