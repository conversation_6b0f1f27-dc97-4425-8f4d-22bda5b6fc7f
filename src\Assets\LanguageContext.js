import React, { createContext, useContext, useState } from 'react';

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en'); // Default language is English

  // Translations for different languages
  const translations = {
    en: {
      welcome: 'Welcome',
      home: 'Home',
      profile: 'Profile',
      settings: 'Settings',
      logout: 'Logout',
    },
    es: {
      welcome: 'Bienvenido',
      home: 'Inicio',
      profile: 'Perfil',
      settings: 'Configuración',
      logout: 'Cerrar sesión',
    },
    fr: {
      welcome: 'Bienvenue',
      home: 'Accueil',
      profile: 'Profil',
      settings: 'Paramètres',
      logout: 'Déconnexion',
    },
    ar: {
      welcome: 'مرحبًا',
      home: 'الصفحة الرئيسية',
      profile: 'الملف الشخصي',
      settings: 'الإعدادات',
      logout: 'تسجيل الخروج',
    },
  };

  const changeLanguage = (lang) => {
    setLanguage(lang);
  };

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, translations }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);