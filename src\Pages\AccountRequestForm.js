import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../Assets/ThemeContext';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import Fade from '@mui/material/Fade';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import PersonIcon from '@mui/icons-material/Person';
import BusinessIcon from '@mui/icons-material/Business';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import DescriptionIcon from '@mui/icons-material/Description';
import InputAdornment from '@mui/material/InputAdornment';

import 'bootstrap/dist/css/bootstrap.min.css';

const AccountRequestForm = () => {
  const { isDarkMode } = useTheme();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    fullName: '',
    department: '',
    position: '',
    email: '',
    phone: '',
    reason: '',
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.fullName) newErrors.fullName = 'Full name is required';
    if (!formData.department) newErrors.department = 'Department is required';
    if (!formData.position) newErrors.position = 'Position is required';
    if (!formData.email) newErrors.email = 'Email is required';
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) newErrors.email = 'Invalid email format';
    if (!formData.reason) newErrors.reason = 'Reason is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      setIsSubmitting(true);
      // Simulate form submission
      setTimeout(() => {
        setOpenSnackbar(true);
        setIsSubmitting(false);
        // Reset form after submission
        setTimeout(() => {
          setFormData({
            fullName: '',
            department: '',
            position: '',
            email: '',
            phone: '',
            reason: '',
          });
        }, 1000);
      }, 1500);
    }
  };

  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      background: {
        default: isDarkMode ? '#121212' : '#f8f9fa',
        paper: isDarkMode ? '#1e1e1e' : '#ffffff',
      },
      primary: {
        main: isDarkMode ? '#90caf9' : '#4361ee',
      },
      secondary: {
        main: isDarkMode ? '#ff6b6b' : '#f72585',
      },
      text: {
        primary: isDarkMode ? '#ffffff' : '#212529',
        secondary: isDarkMode ? '#b0b0b0' : '#6c757d',
      },
    },
    shape: {
      borderRadius: 12,
    },
  });

  const handleCloseSnackbar = () => {
    setOpenSnackbar(false);
  };

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      <Container
        component="main"
        maxWidth="md"
        className="d-flex flex-column justify-content-center vh-100"
        sx={{
          transition: 'all 0.3s ease',
        }}
      >
        <Fade in={true} timeout={800}>
          <Paper
            elevation={isDarkMode ? 0 : 6}
            sx={{
              padding: { xs: 3, sm: 4, md: 6 },
              borderRadius: '24px',
              backgroundColor: theme.palette.background.paper,
              border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
              boxShadow: isDarkMode 
                ? '0 8px 32px rgba(0, 0, 0, 0.5)'
                : '0 8px 32px rgba(0, 0, 0, 0.1)',
              transform: 'translateY(0)',
              '&:hover': {
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: '800',
                  background: isDarkMode 
                    ? 'linear-gradient(90deg, #90caf9, #64b5f6)'
                    : 'linear-gradient(90deg, #4361ee, #3a0ca3)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: 1,
                }}
              >
                Account Request Form
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: theme.palette.text.secondary,
                  marginBottom: 3,
                }}
              >
                Please fill out this form to request access to our systems
              </Typography>
            </div>

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    name="fullName"
                    variant="outlined"
                    value={formData.fullName}
                    onChange={handleChange}
                    error={!!errors.fullName}
                    helperText={errors.fullName}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon sx={{ 
                            color: errors.fullName ? theme.palette.error.main : theme.palette.text.secondary 
                          }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                        },
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Department"
                    name="department"
                    variant="outlined"
                    value={formData.department}
                    onChange={handleChange}
                    error={!!errors.department}
                    helperText={errors.department}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <BusinessIcon sx={{ 
                            color: errors.department ? theme.palette.error.main : theme.palette.text.secondary 
                          }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                        },
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Position"
                    name="position"
                    variant="outlined"
                    value={formData.position}
                    onChange={handleChange}
                    error={!!errors.position}
                    helperText={errors.position}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon sx={{ 
                            color: errors.position ? theme.palette.error.main : theme.palette.text.secondary 
                          }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                        },
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    name="email"
                    type="email"
                    variant="outlined"
                    value={formData.email}
                    onChange={handleChange}
                    error={!!errors.email}
                    helperText={errors.email}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <EmailIcon sx={{ 
                            color: errors.email ? theme.palette.error.main : theme.palette.text.secondary 
                          }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                        },
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone Number (Optional)"
                    name="phone"
                    type="tel"
                    variant="outlined"
                    value={formData.phone}
                    onChange={handleChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PhoneIcon sx={{ color: theme.palette.text.secondary }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                        },
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Reason for Access"
                    name="reason"
                    variant="outlined"
                    multiline
                    rows={4}
                    value={formData.reason}
                    onChange={handleChange}
                    error={!!errors.reason}
                    helperText={errors.reason}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <DescriptionIcon sx={{ 
                            color: errors.reason ? theme.palette.error.main : theme.palette.text.secondary 
                          }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`,
                        },
                        alignItems: 'flex-start',
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate(-1)}
                      sx={{
                        padding: '12px 24px',
                        borderRadius: '12px',
                        textTransform: 'none',
                        fontWeight: '600',
                        borderColor: theme.palette.text.secondary,
                        color: theme.palette.text.primary,
                        '&:hover': {
                          borderColor: theme.palette.primary.main,
                          color: theme.palette.primary.main,
                        },
                      }}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      disabled={isSubmitting}
                      sx={{
                        padding: '14px 28px',
                        fontSize: '16px',
                        fontWeight: '700',
                        borderRadius: '12px',
                        textTransform: 'none',
                        letterSpacing: '0.5px',
                        background: isDarkMode
                          ? 'linear-gradient(135deg, #90caf9, #64b5f6)'
                          : 'linear-gradient(135deg, #4361ee, #3a0ca3)',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        '&:hover': {
                          transform: 'translateY(-1px)',
                          boxShadow: '0 6px 8px rgba(0, 0, 0, 0.15)',
                          background: isDarkMode
                            ? 'linear-gradient(135deg, #64b5f6, #42a5f5)'
                            : 'linear-gradient(135deg, #3a0ca3, #480ca8)',
                        },
                        '&:active': {
                          transform: 'translateY(0)',
                        },
                        transition: 'all 0.2s ease',
                      }}
                    >
                      {isSubmitting ? 'Submitting...' : 'Submit Request'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Fade>

        <Snackbar
          open={openSnackbar}
          autoHideDuration={4000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
          TransitionComponent={Fade}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity="success"
            sx={{ 
              width: '100%',
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              background: isDarkMode
                ? 'linear-gradient(135deg, #2e7d32, #1b5e20)'
                : 'linear-gradient(135deg, #4caf50, #2e7d32)',
              color: '#fff',
              '& .MuiAlert-icon': {
                color: '#fff',
              },
            }}
          >
            Your request has been submitted! The IT department will contact you shortly. 📩
          </Alert>
        </Snackbar>
      </Container>
    </MuiThemeProvider>
  );
};

export default AccountRequestForm;